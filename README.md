# BVR Safaris

A comprehensive platform for hunting and photo safari bookings in South Africa, built with Next.js, Firebase, and enhanced location services.

## Features

### 🎯 Core Functionality
- **Farm Management**: Create, edit, and manage game farms
- **Booking System**: Complete booking workflow for hunting and photo safaris
- **User Authentication**: Secure authentication with role-based access
- **Image Management**: Upload and manage farm images with optimization

### 📍 Location Services (Enhanced)
- **Smart Location Input**: Google Places API integration with South African focus
- **Distance-Based Search**: Find farms within specified radius
- **Automatic Province Detection**: Smart mapping from location data
- **Geographic Filtering**: Advanced location-based search and filtering

## Getting Started

### Prerequisites
- Node.js 18+
- Firebase project with Firestore and Authentication
- Google Places API key (for location services)

### Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Set up environment variables (see [Environment Setup Guide](planning/ENVIRONMENT_SETUP.md))

4. Run the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Documentation

### Setup & Configuration
- [Environment Setup Guide](planning/ENVIRONMENT_SETUP.md) - Complete setup instructions
- [Geolocation Status Summary](planning/GEOLOCATION_STATUS_SUMMARY.md) - Current implementation status
- [Technical Specifications](planning/geolocation-technical-specs.md) - Detailed technical docs

### Development
- [Implementation Plan](planning/geolocation-implementation-plan.md) - Development roadmap
- [Integration Points](planning/geolocation-integration-points.md) - Component integration details

## Testing Location Services

Visit [http://localhost:3000/test/location](http://localhost:3000/test/location) to test:
- Location autocomplete functionality
- Place details retrieval
- Distance calculations
- Search integration

## Architecture

### Location Services
- **GooglePlacesService**: Handles autocomplete and place details
- **DistanceService**: Geographic calculations and utilities
- **LocationAutocomplete**: React component for location input
- **Enhanced Farm Service**: Location-based search and filtering

### Database Schema
- Enhanced farm documents with `LocationData` structure
- Backward compatibility with existing location fields
- Optimized indexes for geographic queries

## Environment Variables

Required environment variables:
```bash
# Google Places API
GOOGLE_PLACES_API_KEY_SERVER=your_server_key
NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=your_client_key

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_key
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
# ... other Firebase config
```

See [Environment Setup Guide](planning/ENVIRONMENT_SETUP.md) for complete configuration details.
