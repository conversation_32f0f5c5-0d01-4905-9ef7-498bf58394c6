# Typography System Documentation

## Overview

The BvR Safaris application uses a modern, two-font typography system designed to enhance readability while complementing the safari imagery and maintaining a professional appearance.

## Font Strategy

### Primary Font: Montserrat
- **Usage**: Headings (h1-h6), Navigation elements, Buttons, Interactive elements, Hero text, Prominent UI components
- **Characteristics**: Modern, geometric sans-serif with excellent legibility at various sizes
- **Weights**: 300, 400, 500, 600, 700, 800
- **Why Montserrat**: Provides a clean, modern appearance that complements safari photography without competing with visual content

### Secondary Font: Source Sans Pro
- **Usage**: Body text, Paragraphs, Form labels and inputs, Card descriptions, General content text
- **Characteristics**: Humanist sans-serif optimized for readability in longer text blocks
- **Weights**: 300, 400, 500, 600, 700
- **Why Source Sans Pro**: Excellent readability for extended reading, professional appearance, works well across all devices

## Implementation Details

### Next.js Font Optimization
```typescript
// apps/web/src/app/layout.tsx
import { <PERSON><PERSON><PERSON>, Source_Sans_3 } from "next/font/google";

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  display: "swap",
  weight: ["300", "400", "500", "600", "700", "800"],
});

const sourceSans = Source_Sans_3({
  variable: "--font-source-sans",
  subsets: ["latin"],
  display: "swap",
  weight: ["300", "400", "500", "600", "700"],
});
```

### CSS Variables
```css
/* apps/web/src/app/globals.css */
:root {
  --font-display: var(--font-montserrat), 'Segoe UI', system-ui, sans-serif;
  --font-body: var(--font-source-sans), 'Segoe UI', system-ui, sans-serif;
  --font-ui: var(--font-montserrat), 'Segoe UI', system-ui, sans-serif;
}
```

### Tailwind Configuration
```typescript
// apps/web/tailwind.config.ts
fontFamily: {
  sans: ["var(--font-source-sans)", "Segoe UI", "system-ui", "sans-serif"],
  serif: ["Georgia", "serif"],
  mono: ["var(--font-source-sans)", "-apple-system", "sans-serif"],
  display: ["var(--font-montserrat)", "Segoe UI", "system-ui", "sans-serif"],
  ui: ["var(--font-montserrat)", "Segoe UI", "system-ui", "sans-serif"],
}
```

## Typography Hierarchy

### Headings (Montserrat)
- **H1**: 3rem (48px) desktop, 2rem (32px) mobile - Main page titles
- **H2**: 2.5rem (40px) desktop, 1.75rem (28px) mobile - Section headings
- **H3**: 2rem (32px) desktop, 1.5rem (24px) mobile - Subsection headings
- **H4**: 1.5rem (24px) - Component titles
- **H5**: 1.25rem (20px) - Small section titles
- **H6**: 1rem (16px) - Minor headings

### Body Text (Source Sans Pro)
- **Regular**: 1rem (16px) - Standard body text
- **Large**: 1.125rem (18px) - Featured content, important announcements
- **Small**: 0.875rem (14px) - Captions, disclaimers, secondary information

## Usage Guidelines

### When to Use Montserrat
- All heading elements (h1-h6)
- Navigation menu items
- Button text
- Form labels
- Brand/logo text
- Call-to-action elements
- Hero section text
- Card titles
- Badge text

### When to Use Source Sans Pro
- Paragraph text
- Article content
- Form input text
- Card descriptions
- List items
- Table content
- Footer text
- Long-form content

## Component Examples

### Using CSS Variables in Components
```tsx
// For headings and UI elements
className="font-[var(--font-display)]"

// For body text
className="font-[var(--font-body)]"

// For UI elements like buttons
className="font-[var(--font-ui)]"
```

### Using Tailwind Classes
```tsx
// For headings and UI elements
className="font-display"

// For body text
className="font-sans"

// For UI elements
className="font-ui"
```

## Accessibility Considerations

- **Font Size**: Minimum 16px for body text to ensure readability
- **Line Height**: 1.6 for body text, 1.2 for headings
- **Contrast**: All text meets WCAG AA contrast requirements
- **Fallbacks**: System fonts provide fallbacks if Google Fonts fail to load

## Performance Optimization

- **Font Display**: Uses `swap` for immediate text rendering
- **Preloading**: Next.js automatically optimizes font loading
- **Subsetting**: Only Latin characters loaded to reduce file size
- **Variable Fonts**: Not used to maintain broader browser support

## Browser Support

- **Modern Browsers**: Full support with Google Fonts
- **Fallback**: Segoe UI and system fonts for older browsers
- **Progressive Enhancement**: Text remains readable even if custom fonts fail

## Testing

Visit `/typography-test` to see all typography elements in action and verify proper font loading.

## Maintenance

- Font weights can be adjusted in `layout.tsx`
- CSS variables can be modified in `globals.css`
- Tailwind font families can be updated in `tailwind.config.ts`
- Always test changes across different devices and browsers
