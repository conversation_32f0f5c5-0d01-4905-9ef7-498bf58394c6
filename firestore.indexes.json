{"indexes": [{"collectionGroup": "farms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "featured", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "farms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "province", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "farms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "activityTypes", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "farms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "ownerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hunterId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "farmId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hunterId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "bookings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "farmId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "farmId", "order": "ASCENDING"}, {"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "reviewerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "bookingId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "recipientId", "order": "ASCENDING"}, {"fieldPath": "readAt", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "availability", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "farmId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "images", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "farmId", "order": "ASCENDING"}, {"fieldPath": "displayOrder", "order": "ASCENDING"}]}, {"collectionGroup": "images", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "farmId", "order": "ASCENDING"}, {"fieldPath": "isPrimary", "order": "DESCENDING"}, {"fieldPath": "displayOrder", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "farms", "fieldPath": "name", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "farms", "fieldPath": "description", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "species", "fieldPath": "name", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}]}