rules_version = '2';

// Firestore Security Rules for BVR Safaris
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return request.auth != null &&
             request.auth.token.role == 'admin';
    }

    function isFarmOwner() {
      return request.auth != null &&
             (request.auth.token.role == 'farm_owner' || request.auth.token.role == 'admin');
    }

    function isGuest() {
      return request.auth != null &&
             (request.auth.token.role == 'guest' || request.auth.token.role == 'admin');
    }

    // Legacy function for backward compatibility
    function isHunter() {
      return isGuest();
    }
    
    // User profiles collection
    match /users/{userId} {
      // Users can read and write their own profile
      allow read, write: if isOwner(userId);
      
      // Authenticated users can read other users' basic profile info
      allow read: if isAuthenticated();
      
      // Admins can read and write all profiles
      allow read, write: if isAdmin();
    }
    
    // Farms collection
    match /farms/{farmId} {
      // Anyone can read active farms (for browsing)
      allow read: if resource.data.isActive == true;

      // Authenticated users can read all farms
      allow read: if isAuthenticated();

      // Allow list operations for browsing farms
      allow list: if true; // Anyone can list farms for browsing
      
      // Farm owners can create farms
      allow create: if isFarmOwner() && 
                       request.auth.uid == request.resource.data.ownerId;
      
      // Farm owners can update their own farms
      allow update: if isFarmOwner() && 
                       request.auth.uid == resource.data.ownerId;
      
      // Farm owners can delete their own farms
      allow delete: if isFarmOwner() && 
                       request.auth.uid == resource.data.ownerId;
      
      // Admins can do everything
      allow read, write: if isAdmin();
      
      // Farm images subcollection
      match /images/{imageId} {
        allow read: if isAuthenticated();
        allow write: if isFarmOwner() && 
                        request.auth.uid == get(/databases/$(database)/documents/farms/$(farmId)).data.ownerId;
        allow delete: if isFarmOwner() && 
                         request.auth.uid == get(/databases/$(database)/documents/farms/$(farmId)).data.ownerId;
      }
      
      // Farm reviews subcollection
      match /reviews/{reviewId} {
        // Anyone can read public reviews
        allow read: if resource.data.isPublic == true;
        
        // Authenticated users can read all reviews
        allow read: if isAuthenticated();
        
        // Users can create reviews for their own bookings
        allow create: if isAuthenticated() && 
                         request.auth.uid == request.resource.data.reviewerId;
        
        // Users can update their own reviews
        allow update: if isAuthenticated() && 
                         request.auth.uid == resource.data.reviewerId;
        
        // Farm owners can respond to reviews on their farms
        allow update: if isFarmOwner() && 
                         request.auth.uid == get(/databases/$(database)/documents/farms/$(farmId)).data.ownerId &&
                         request.resource.data.diff(resource.data).affectedKeys().hasOnly(['responseFromOwner', 'respondedAt']);
      }
      
      // Farm availability subcollection
      match /availability/{availabilityId} {
        allow read: if isAuthenticated();
        allow write: if isFarmOwner() && 
                        request.auth.uid == get(/databases/$(database)/documents/farms/$(farmId)).data.ownerId;
      }
      
      // Farm species subcollection
      match /species/{speciesId} {
        allow read: if isAuthenticated();
        allow write: if isFarmOwner() && 
                        request.auth.uid == get(/databases/$(database)/documents/farms/$(farmId)).data.ownerId;
      }
      
      // Farm accommodations subcollection
      match /accommodations/{accommodationId} {
        allow read: if isAuthenticated();
        allow write: if isFarmOwner() && 
                        request.auth.uid == get(/databases/$(database)/documents/farms/$(farmId)).data.ownerId;
      }
      
      // Farm amenities subcollection
      match /amenities/{amenityId} {
        allow read: if isAuthenticated();
        allow write: if isFarmOwner() && 
                        request.auth.uid == get(/databases/$(database)/documents/farms/$(farmId)).data.ownerId;
      }
    }
    
    // Bookings collection
    match /bookings/{bookingId} {
      // Users can read their own bookings (as hunter)
      allow read: if isAuthenticated() &&
                     request.auth.uid == resource.data.hunterId;

      // Farm owners can read bookings for their farms
      allow read: if isFarmOwner() &&
                     request.auth.uid == get(/databases/$(database)/documents/farms/$(resource.data.farmId)).data.ownerId;

      // Allow authenticated users to query bookings (application will filter appropriately)
      allow list: if isAuthenticated();
      
      // Guests (hunters, photo safari guests, visitors) can create bookings
      allow create: if isGuest() &&
                       request.auth.uid == request.resource.data.hunterId;
      
      // Hunters can update their own bookings (limited fields)
      allow update: if isAuthenticated() && 
                       request.auth.uid == resource.data.hunterId &&
                       request.resource.data.diff(resource.data).affectedKeys().hasOnly(['specialRequests']);
      
      // Farm owners can update booking status
      allow update: if isFarmOwner() && 
                       request.auth.uid == get(/databases/$(database)/documents/farms/$(resource.data.farmId)).data.ownerId &&
                       request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'confirmedAt', 'cancelledAt', 'cancellationReason']);
      
      // Admins can do everything
      allow read, write: if isAdmin();
      
      // Booking messages subcollection
      match /messages/{messageId} {
        // Participants can read messages
        allow read: if isAuthenticated() && 
                       (request.auth.uid == resource.data.senderId || 
                        request.auth.uid == resource.data.recipientId);
        
        // Participants can create messages
        allow create: if isAuthenticated() && 
                         request.auth.uid == request.resource.data.senderId;
        
        // Recipients can mark messages as read
        allow update: if isAuthenticated() && 
                         request.auth.uid == resource.data.recipientId &&
                         request.resource.data.diff(resource.data).affectedKeys().hasOnly(['readAt']);
      }
    }
    
    // Global collections (read-only for most users)
    match /species/{speciesId} {
      allow read: if true; // Public read access
      allow write: if isAdmin();
    }
    
    match /amenities/{amenityId} {
      allow read: if true; // Public read access
      allow write: if isAdmin();
    }
    
    match /accommodationTypes/{typeId} {
      allow read: if true; // Public read access
      allow write: if isAdmin();
    }
    
    // Upload analytics collection (used by Firebase Cloud Functions)
    match /uploadAnalytics/{analyticsId} {
      // Only allow Cloud Functions to write analytics data
      // Users can read their own analytics data
      allow read: if isAuthenticated() &&
                     request.auth.uid == resource.data.userId;
      allow write: if false; // Only Cloud Functions can write
    }

    // Admin-only collections
    match /admin/{document=**} {
      allow read, write: if isAdmin();
    }

    // Fallback rule - deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
