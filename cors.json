[{"origin": ["*"], "method": ["GET", "HEAD", "OPTIONS"], "maxAgeSeconds": 3600, "responseHeader": ["Content-Type", "Access-Control-Allow-Origin", "x-goog-resumable"]}, {"origin": ["http://localhost:3000", "http://localhost:3001", "https://localhost:3000", "https://bvr-safaris.vercel.app", "bvr-safaris-git-feature-firbase-7d2165-pienaar-ankers-projects.vercel.app"], "method": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"], "maxAgeSeconds": 3600, "responseHeader": ["Content-Type", "Authorization", "Content-Length", "User-Agent", "x-goog-resumable", "Access-Control-Allow-Origin"]}]