# BVR Safaris Geolocation Technical Specifications

## API Integration Specifications

### Google Places API (New) Configuration

**Required APIs:**
- Places API (New) - Primary service
- Maps JavaScript API - For map displays (optional enhancement)

**API Key Configuration:**
```typescript
// Environment Variables
NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=your_api_key_here
GOOGLE_PLACES_API_KEY_SERVER=your_server_api_key_here // For server-side calls

// API Restrictions (Security)
- HTTP referrers: https://bvr-safaris.vercel.app/*, http://localhost:3000/*
- Application restrictions: None (for development)
- API restrictions: Places API (New), Maps JavaScript API
```

**Rate Limiting Strategy:**
```typescript
interface RateLimitConfig {
  autocomplete: {
    requestsPerSecond: 10
    burstLimit: 50
    debounceMs: 300
  }
  placeDetails: {
    requestsPerSecond: 5
    burstLimit: 20
    cacheTimeMs: 300000 // 5 minutes
  }
}
```

## Core Service Implementation

### Location Service Architecture

```typescript
// src/lib/services/location/googlePlaces.ts
export class GooglePlacesService {
  private apiKey: string
  private rateLimiter: RateLimiter
  private cache: LocationCache

  // Autocomplete for address suggestions
  async getPlacePredictions(
    input: string,
    options?: {
      types?: string[] // ['establishment', 'geocode']
      componentRestrictions?: { country: string } // 'za' for South Africa
      locationBias?: { lat: number; lng: number; radius: number }
    }
  ): Promise<LocationSearchResult[]>

  // Get detailed place information
  async getPlaceDetails(
    placeId: string,
    fields?: string[] // ['formatted_address', 'geometry', 'address_components']
  ): Promise<PlaceDetails>

  // Geocode address to coordinates
  async geocodeAddress(address: string): Promise<GeocodeResult>

  // Reverse geocode coordinates to address
  async reverseGeocode(lat: number, lng: number): Promise<PlaceDetails>
}
```

### Distance Calculation Utilities

```typescript
// src/lib/services/location/distance.ts
export class DistanceService {
  // Haversine formula for accurate distance calculation
  static calculateDistance(
    point1: { lat: number; lng: number },
    point2: { lat: number; lng: number }
  ): number // Returns distance in kilometers

  // Check if point is within radius
  static isWithinRadius(
    center: { lat: number; lng: number },
    point: { lat: number; lng: number },
    radiusKm: number
  ): boolean

  // Calculate bounding box for efficient queries
  static getBoundingBox(
    center: { lat: number; lng: number },
    radiusKm: number
  ): {
    northeast: { lat: number; lng: number }
    southwest: { lat: number; lng: number }
  }
}
```

## Database Schema Implementation

### Firestore Schema Updates

```typescript
// Enhanced Farm Document Structure
interface EnhancedGameFarm {
  // Existing fields maintained for backward compatibility
  id: string
  ownerId: string
  name: string
  location: string // Keep existing text location
  province: SouthAfricanProvince
  coordinates?: string // Legacy field, deprecated

  // New structured location data
  locationData?: {
    placeId: string
    formattedAddress: string
    coordinates: GeoPoint // Firestore GeoPoint for geo queries
    addressComponents: {
      streetNumber?: string
      route?: string
      locality?: string
      sublocality?: string
      administrativeAreaLevel1: string // Province
      administrativeAreaLevel2?: string // District/Municipality
      postalCode?: string
      country: string
    }
    placeTypes: string[]
    viewport?: {
      northeast: GeoPoint
      southwest: GeoPoint
    }
  }

  // Search optimization fields
  searchableLocation: string // Concatenated searchable text
  geoHash: string // For efficient geographic queries
  
  // Existing fields...
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### Firestore Indexes Required

Add these indexes to `firestore.indexes.json`:

```json
{
  "indexes": [
    {
      "collectionGroup": "farms",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "locationData.coordinates",
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "farms",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "province",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "locationData.coordinates",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "farms",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "searchableLocation",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "farms",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "geoHash",
          "order": "ASCENDING"
        }
      ]
    }
  ]
}
```

## Component Specifications

### LocationAutocomplete Component

```typescript
// src/components/location/LocationAutocomplete.tsx
interface LocationAutocompleteProps {
  value?: string
  onLocationSelect: (location: PlaceDetails) => void
  onInputChange?: (input: string) => void
  placeholder?: string
  countryRestriction?: string // Default: 'za'
  types?: string[] // Default: ['establishment', 'geocode']
  className?: string
  disabled?: boolean
  required?: boolean
  error?: string
}

export function LocationAutocomplete({
  value,
  onLocationSelect,
  onInputChange,
  placeholder = "Enter location or address...",
  countryRestriction = 'za',
  types = ['establishment', 'geocode'],
  className,
  disabled = false,
  required = false,
  error
}: LocationAutocompleteProps) {
  // Implementation with debounced search
  // Dropdown with suggestions
  // Keyboard navigation support
  // Loading states and error handling
}
```

### Enhanced Search Implementation

```typescript
// src/lib/services/search/locationSearch.ts
export class LocationSearchService {
  async searchFarmsWithLocation(params: {
    query?: string
    location?: {
      center: { lat: number; lng: number }
      radius: number // kilometers
    }
    bounds?: {
      northeast: { lat: number; lng: number }
      southwest: { lat: number; lng: number }
    }
    filters: {
      provinces?: string[]
      activities?: string[]
      priceRange?: [number, number]
    }
    limit?: number
    sortBy?: 'distance' | 'rating' | 'created' | 'name'
  }): Promise<{
    farms: (GameFarm & { distance?: number })[]
    total: number
    hasMore: boolean
  }>

  // Helper method for distance-based sorting
  private sortByDistance(
    farms: GameFarm[],
    center: { lat: number; lng: number }
  ): (GameFarm & { distance: number })[]
}
```

## Environment Configuration

### Required Environment Variables

```bash
# Google Places API Configuration
GOOGLE_PLACES_API_KEY_SERVER=your_server_side_api_key_here
NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=your_client_side_api_key_here

# API Key Restrictions (for security)
# HTTP referrers: https://bvr-safaris.vercel.app/*, http://localhost:3000/*
# Application restrictions: None (for development)
# API restrictions: Places API (New), Maps JavaScript API
```

### Google Cloud Console Setup

1. **Enable Required APIs:**
   - Places API (New)
   - Maps JavaScript API (optional for future map features)

2. **Create API Keys:**
   - Server-side key: Restrict by IP addresses (production)
   - Client-side key: Restrict by HTTP referrers

3. **Set Usage Quotas:**
   - Autocomplete: 1000 requests/day (development)
   - Place Details: 1000 requests/day (development)
   - Increase limits for production deployment

## Performance Optimization

### Caching Strategy

```typescript
// src/lib/services/location/cache.ts
export class LocationCache {
  private cache = new Map<string, CacheEntry>()
  private readonly TTL = 5 * 60 * 1000 // 5 minutes

  set(key: string, value: any): void
  get(key: string): any | null
  clear(): void
  
  // Specific caching for common searches
  cacheAutocomplete(input: string, results: LocationSearchResult[]): void
  getCachedAutocomplete(input: string): LocationSearchResult[] | null
}
```

### Request Optimization

```typescript
// Debounced autocomplete requests
const debouncedSearch = useMemo(
  () => debounce(async (input: string) => {
    if (input.length < 3) return []
    
    try {
      const results = await locationService.getPlacePredictions(input, {
        componentRestrictions: { country: 'za' },
        types: ['establishment', 'geocode']
      })
      return results
    } catch (error) {
      console.error('Autocomplete error:', error)
      return []
    }
  }, 300),
  []
)
```

## Error Handling & Fallbacks

### Graceful Degradation

```typescript
// Fallback strategies when Google API is unavailable
export class LocationServiceWithFallback {
  async getPlacePredictions(input: string): Promise<LocationSearchResult[]> {
    try {
      return await this.googlePlacesService.getPlacePredictions(input)
    } catch (error) {
      console.warn('Google Places API unavailable, using fallback')
      return this.fallbackLocationSearch(input)
    }
  }

  private fallbackLocationSearch(input: string): LocationSearchResult[] {
    // Use existing province/location text matching
    // Return formatted results matching the interface
  }
}
```

## Testing Strategy

### Unit Tests
- Location service methods
- Distance calculations
- Cache functionality
- Component rendering and interactions

### Integration Tests
- Google API integration
- Database queries with location data
- End-to-end search functionality

### Performance Tests
- API response times
- Database query performance
- Component rendering performance
- Memory usage monitoring

This technical specification provides the detailed implementation guidelines for the geolocation functionality integration.
