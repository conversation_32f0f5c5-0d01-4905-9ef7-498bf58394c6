# BVR Safaris Environment Setup Guide

## Google Places API Configuration

### Prerequisites
- Google Cloud Platform account
- Firebase project for BVR Safaris
- Billing enabled on Google Cloud Platform

### Step 1: Enable Required APIs

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to **APIs & Services > Library**
4. Enable the following APIs:
   - **Places API (New)** - Primary service for location functionality
   - **Maps JavaScript API** - For future map features (optional)

### Step 2: Create API Keys

#### Server-Side API Key
1. Go to **APIs & Services > Credentials**
2. Click **Create Credentials > API Key**
3. Name it: `BVR Safaris Server Key`
4. **Restrict the key:**
   - Application restrictions: **IP addresses**
   - Add your server IPs (for production)
   - For development: No restrictions
   - API restrictions: **Places API (New)**

#### Client-Side API Key
1. Create another API key
2. Name it: `BVR Safaris Client Key`
3. **Restrict the key:**
   - Application restrictions: **HTTP referrers**
   - Add: `https://bvr-safaris.vercel.app/*`
   - Add: `http://localhost:3000/*` (for development)
   - Add: `https://localhost:3000/*` (for HTTPS development)
   - API restrictions: **Places API (New)**, **Maps JavaScript API**

### Step 3: Environment Variables

Create/update your `.env.local` file:

```bash
# Google Places API Keys
GOOGLE_PLACES_API_KEY_SERVER=your_server_side_api_key_here
NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=your_client_side_api_key_here

# Firebase Configuration (if not already set)
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

### Step 4: Set Usage Quotas (Development)

1. Go to **APIs & Services > Quotas**
2. Search for "Places API (New)"
3. Set appropriate limits:
   - **Autocomplete requests**: 1,000/day (development)
   - **Place Details requests**: 1,000/day (development)
   - **Geocoding requests**: 500/day (development)

For production, increase these limits based on expected usage.

## Firestore Indexes Setup

### Required Indexes

Add the following indexes to your `firestore.indexes.json` file:

```json
{
  "indexes": [
    {
      "collectionGroup": "farms",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "locationData.coordinates",
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "farms",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "province",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "locationData.coordinates",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "farms",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "searchableLocation",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "farms",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "isActive",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "geoHash",
          "order": "ASCENDING"
        }
      ]
    }
  ]
}
```

### Deploy Indexes

```bash
# Deploy the indexes to Firebase
firebase deploy --only firestore:indexes
```

## Development Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Start Development Server
```bash
npm run dev
```

### 3. Test Location Services
Navigate to: `http://localhost:3000/test/location`

This page provides a comprehensive test interface for:
- Location autocomplete functionality
- Place details retrieval
- Distance calculations
- Search integration

## Verification Checklist

### ✅ API Configuration
- [ ] Places API (New) enabled in Google Cloud Console
- [ ] API keys created with proper restrictions
- [ ] Environment variables set in `.env.local`
- [ ] API quotas configured appropriately

### ✅ Firebase Setup
- [ ] Firestore indexes deployed
- [ ] Firebase project connected
- [ ] Authentication working

### ✅ Application Testing
- [ ] Location autocomplete working on test page
- [ ] Farm creation form accepts location input
- [ ] Search functionality includes location filtering
- [ ] No console errors related to API keys

## Troubleshooting

### Common Issues

#### "API key not valid" Error
- Check that the API key is correctly set in environment variables
- Verify the key has the correct API restrictions
- Ensure the key has the correct application restrictions

#### "This API project is not authorized" Error
- Check that Places API (New) is enabled
- Verify billing is enabled on the Google Cloud project
- Check API quotas haven't been exceeded

#### Location Autocomplete Not Working
- Check browser console for API errors
- Verify the client-side API key is properly configured
- Check that HTTP referrer restrictions include your domain

#### Slow Query Performance
- Verify Firestore indexes are deployed
- Check that location-based queries use proper indexes
- Monitor query performance in Firebase console

### Getting Help

1. Check the [Google Places API documentation](https://developers.google.com/maps/documentation/places/web-service/overview)
2. Review Firebase [Firestore documentation](https://firebase.google.com/docs/firestore)
3. Check the application logs for specific error messages
4. Test individual components using the test page at `/test/location`

## Production Deployment

### Security Considerations
- Use IP restrictions for server-side API keys
- Use HTTP referrer restrictions for client-side keys
- Monitor API usage and set up alerts
- Implement rate limiting in the application

### Monitoring
- Set up Google Cloud monitoring for API usage
- Monitor Firebase usage and costs
- Implement error logging for location services
- Track user experience metrics for location features
