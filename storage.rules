rules_version = '2';

// Firebase Storage Security Rules for BVR Safaris
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user owns the resource
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && 
             request.auth.token.role == 'admin';
    }
    
    // Helper function to check if user is farm owner
    function isFarmOwner() {
      return request.auth != null && 
             (request.auth.token.role == 'farm_owner' || request.auth.token.role == 'admin');
    }
    
    // Helper function to validate image file types
    function isValidImageType() {
      return resource.contentType.matches('image/.*');
    }
    
    // Helper function to validate file size (in bytes)
    function isValidSize(maxSizeBytes) {
      return resource.size <= maxSizeBytes;
    }

    // Hero Images Rules - Public read access for website hero sections
    match /hero-images/{imageId} {
      // Allow public read access for hero images (used in website hero sections)
      allow read: if true;

      // Only admins can upload/manage hero images
      allow write, delete: if isAdmin();
    }

    // Farm Images Rules
    match /farm-images/{farmId}/{imageId} {
      // Allow read access to all authenticated users (for browsing farms)
      allow read: if isAuthenticated();

      // Allow authenticated users to upload images (app handles role validation)
      // Max size: 50MB (52,428,800 bytes)
      allow write: if isAuthenticated() &&
                      isValidImageType() &&
                      isValidSize(52428800);

      // Allow authenticated users to delete farm images (app handles ownership validation)
      allow delete: if isAuthenticated();
    }
    
    // Profile Images Rules
    match /profile-images/{userId}/{imageId} {
      // Allow users to read their own profile images
      // Allow authenticated users to read other users' profile images
      allow read: if isAuthenticated();
      
      // Allow users to upload images to their own profile
      // Max size: 10MB (10,485,760 bytes)
      allow write: if isOwner(userId) && 
                      isValidImageType() && 
                      isValidSize(10485760);
      
      // Allow users to delete their own profile images
      allow delete: if isOwner(userId);
    }
    
    // Admin access to all files
    match /{allPaths=**} {
      allow read, write, delete: if isAdmin();
    }
    
    // Fallback rule - deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
