#!/bin/bash

# BVR Safaris Cloud Functions Setup Script
# This script helps set up and deploy Firebase Cloud Functions for image uploads

set -e

echo "🚀 BVR Safaris Cloud Functions Setup"
echo "===================================="

# Check if we're in the right directory
if [ ! -f "firebase.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Error: Firebase CLI is not installed"
    echo "Please install it with: npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "🔐 Please log in to Firebase CLI"
    firebase login
fi

echo "📦 Installing Cloud Functions dependencies..."
cd functions

# Install dependencies
if [ ! -d "node_modules" ]; then
    echo "Installing npm dependencies..."
    npm install
else
    echo "Dependencies already installed, updating..."
    npm update
fi

echo "🔧 Building Cloud Functions..."
npm run build

echo "🧪 Running linter..."
npm run lint

echo "✅ Cloud Functions built successfully!"

# Go back to root
cd ..

echo ""
echo "🔥 Firebase Project Setup"
echo "========================"

# Get current project
CURRENT_PROJECT=$(firebase use --json 2>/dev/null | jq -r '.result.current // empty' 2>/dev/null || echo "")

if [ -z "$CURRENT_PROJECT" ]; then
    echo "No Firebase project selected. Please select a project:"
    firebase use --add
    CURRENT_PROJECT=$(firebase use --json | jq -r '.result.current')
fi

echo "Using Firebase project: $CURRENT_PROJECT"

echo ""
echo "⚙️  Configuration Options"
echo "========================"

read -p "Do you want to set up Firebase Functions configuration? (y/n): " setup_config

if [ "$setup_config" = "y" ] || [ "$setup_config" = "Y" ]; then
    echo "Setting up Firebase Functions configuration..."
    
    # Set default configuration
    firebase functions:config:set images.max_farm_size_mb=50
    firebase functions:config:set images.max_profile_size_mb=10
    firebase functions:config:set images.quality=85
    firebase functions:config:set images.max_farm_dimension=2048
    firebase functions:config:set images.max_profile_dimension=512
    
    echo "✅ Configuration set successfully!"
fi

echo ""
echo "🚀 Deployment Options"
echo "===================="

read -p "Do you want to deploy the Cloud Functions now? (y/n): " deploy_now

if [ "$deploy_now" = "y" ] || [ "$deploy_now" = "Y" ]; then
    echo "Deploying Cloud Functions..."
    firebase deploy --only functions
    
    if [ $? -eq 0 ]; then
        echo "✅ Cloud Functions deployed successfully!"
        
        # Get the function URL
        FUNCTION_URL="https://us-central1-$CURRENT_PROJECT.cloudfunctions.net/uploadImage"
        echo ""
        echo "📡 Your upload function is available at:"
        echo "$FUNCTION_URL"
    else
        echo "❌ Deployment failed. Please check the error messages above."
        exit 1
    fi
else
    echo "Skipping deployment. You can deploy later with:"
    echo "firebase deploy --only functions"
fi

echo ""
echo "🧪 Testing Setup"
echo "==============="

read -p "Do you want to start the emulator for local testing? (y/n): " start_emulator

if [ "$start_emulator" = "y" ] || [ "$start_emulator" = "Y" ]; then
    echo "Starting Firebase emulators..."
    echo "The emulator will run on http://localhost:5001"
    echo "Press Ctrl+C to stop the emulator"
    echo ""
    
    cd functions
    npm run serve
else
    echo "You can start the emulator later with:"
    echo "cd functions && npm run serve"
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Next steps:"
echo "1. Update your .env.local file with the correct Firebase project ID"
echo "2. Test the upload functionality in your web application"
echo "3. Monitor the Cloud Function logs with: firebase functions:log"
echo ""
echo "For troubleshooting, see: CLOUD_FUNCTIONS_MIGRATION.md"
