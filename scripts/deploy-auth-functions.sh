#!/bin/bash

# Deploy Authentication Cloud Functions
# This script deploys the new authentication-related Cloud Functions

set -e

echo "🚀 Deploying Authentication Cloud Functions"
echo "==========================================="

# Check if we're in the right directory
if [ ! -f "firebase.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Error: Firebase CLI is not installed"
    echo "Please install it with: npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "🔐 Please log in to Firebase CLI"
    firebase login
fi

# Get current project
CURRENT_PROJECT=$(firebase use --json | jq -r '.result.current // empty')

if [ -z "$CURRENT_PROJECT" ]; then
    echo "❌ Error: No Firebase project selected"
    echo "Please run: firebase use <project-id>"
    exit 1
fi

echo "Using Firebase project: $CURRENT_PROJECT"

# Check if required environment variables are set
echo ""
echo "🔍 Checking environment variables..."

if [ -z "$FIREBASE_SERVICE_ACCOUNT_KEY" ]; then
    echo "⚠️  Warning: FIREBASE_SERVICE_ACCOUNT_KEY not set"
    echo "   This is required for the Cloud Functions to work properly"
fi

if [ -z "$NEXT_PUBLIC_FIREBASE_PROJECT_ID" ]; then
    echo "⚠️  Warning: NEXT_PUBLIC_FIREBASE_PROJECT_ID not set"
    echo "   This should match your current Firebase project: $CURRENT_PROJECT"
fi

# Build functions
echo ""
echo "🔨 Building Cloud Functions..."
cd functions
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix the errors and try again."
    exit 1
fi

cd ..

# Deploy only the new authentication functions
echo ""
echo "🚀 Deploying authentication functions..."

# Deploy specific functions
firebase deploy --only functions:setUserRole,functions:healthCheck,functions:uploadImage

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Cloud Functions deployed successfully!"
    echo ""
    echo "📡 Your functions are available at:"
    echo "   Health Check: https://us-central1-$CURRENT_PROJECT.cloudfunctions.net/healthCheck"
    echo "   Set User Role: https://us-central1-$CURRENT_PROJECT.cloudfunctions.net/setUserRole"
    echo "   Upload Image: https://us-central1-$CURRENT_PROJECT.cloudfunctions.net/uploadImage"
    echo ""
    echo "🧪 Test the authentication flow:"
    echo "   1. Register a new user in your app"
    echo "   2. Check browser console for role setting success"
    echo "   3. Run: node scripts/test-auth-flow.js"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Test user registration in your app"
    echo "   2. Verify custom claims are set correctly"
    echo "   3. Test role-based permissions"
else
    echo "❌ Deployment failed. Please check the error messages above."
    echo ""
    echo "🔧 Common issues:"
    echo "   1. Make sure you're logged in: firebase login"
    echo "   2. Check project selection: firebase use <project-id>"
    echo "   3. Verify billing is enabled for your Firebase project"
    echo "   4. Check function code for syntax errors"
    exit 1
fi

echo ""
echo "🎉 Authentication fixes deployment complete!"
