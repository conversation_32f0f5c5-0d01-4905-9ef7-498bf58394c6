#!/usr/bin/env node

/**
 * Migration script to transfer data from Supabase to Firebase
 *
 * This script handles:
 * 1. Exporting data from Supabase PostgreSQL
 * 2. Transforming relational data to document structure
 * 3. Importing data to Firestore
 * 4. Migrating files from Supabase Storage to Firebase Storage
 *
 * Usage: npm run migrate:firebase
 */

import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'
import { initializeApp, cert } from 'firebase-admin/app'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { getStorage } from 'firebase-admin/storage'
import { getAuth } from 'firebase-admin/auth'
import * as fs from 'fs'
import * as path from 'path'
import fetch from 'node-fetch'

// Load environment variables
dotenv.config({ path: '.env.local' })

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const FIREBASE_PROJECT_ID = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!
const FIREBASE_STORAGE_BUCKET = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET!
const FIREBASE_PRIVATE_KEY = process.env.FIREBASE_PRIVATE_KEY!
const FIREBASE_CLIENT_EMAIL = process.env.FIREBASE_CLIENT_EMAIL!

// Create Firebase service account object
const FIREBASE_SERVICE_ACCOUNT = {
  type: "service_account",
  project_id: FIREBASE_PROJECT_ID,
  private_key: FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
  client_email: FIREBASE_CLIENT_EMAIL,
}

// Initialize clients
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

const firebaseApp = initializeApp({
  credential: cert(FIREBASE_SERVICE_ACCOUNT),
  projectId: FIREBASE_PROJECT_ID,
  storageBucket: FIREBASE_STORAGE_BUCKET,
})

const firestore = getFirestore(firebaseApp)
const storage = getStorage(firebaseApp)
const auth = getAuth(firebaseApp)

interface MigrationStats {
  users: number
  farms: number
  bookings: number
  reviews: number
  species: number
  amenities: number
  files: number
  errors: string[]
}

class FirebaseMigration {
  private stats: MigrationStats = {
    users: 0,
    farms: 0,
    bookings: 0,
    reviews: 0,
    species: 0,
    amenities: 0,
    files: 0,
    errors: []
  }

  // Main migration method
  async migrate() {
    console.log('🚀 Starting Firebase migration...')

    try {
      // Step 1: Migrate reference data (species, amenities, accommodation types)
      await this.migrateReferenceData()

      // Step 2: Migrate users
      await this.migrateUsers()

      // Step 3: Migrate farms
      await this.migrateFarms()

      // Step 4: Migrate bookings
      await this.migrateBookings()

      // Step 5: Migrate reviews
      await this.migrateReviews()

      // Step 6: Migrate files
      await this.migrateFiles()

      console.log('✅ Migration completed successfully!')
      this.printStats()

    } catch (error) {
      console.error('❌ Migration failed:', error)
      this.stats.errors.push(`Migration failed: ${error}`)
      throw error
    }
  }

  // Migrate reference data (species, amenities, accommodation types)
  private async migrateReferenceData() {
    console.log('📋 Migrating reference data...')

    try {
      // Migrate game species
      const { data: species } = await supabase.from('game_species').select('*')
      if (species) {
        for (const item of species) {
          await firestore.collection('species').doc(item.id).set({
            name: item.name,
            nameAfrikaans: item.name_afrikaans,
            scientificName: item.scientific_name,
            category: item.category,
            description: item.description,
            imageUrl: item.image_url,
            createdAt: new Date(item.created_at)
          })
          this.stats.species++
        }
      }

      // Migrate farm amenities
      const { data: amenities } = await supabase.from('farm_amenities').select('*')
      if (amenities) {
        for (const item of amenities) {
          await firestore.collection('amenities').doc(item.id).set({
            name: item.name,
            nameAfrikaans: item.name_afrikaans,
            icon: item.icon,
            description: item.description,
            createdAt: new Date(item.created_at)
          })
          this.stats.amenities++
        }
      }

      // Migrate accommodation types
      const { data: accommodationTypes } = await supabase.from('accommodation_types').select('*')
      if (accommodationTypes) {
        for (const item of accommodationTypes) {
          await firestore.collection('accommodationTypes').doc(item.id).set({
            name: item.name,
            nameAfrikaans: item.name_afrikaans,
            description: item.description,
            createdAt: new Date(item.created_at)
          })
        }
      }

      console.log(`✅ Migrated ${this.stats.species} species and ${this.stats.amenities} amenities`)

    } catch (error) {
      console.error('❌ Error migrating reference data:', error)
      this.stats.errors.push(`Reference data migration error: ${error}`)
      throw error
    }
  }

  // Migrate users
  private async migrateUsers() {
    console.log('👥 Migrating users...')

    try {
      const { data: profiles } = await supabase.from('profiles').select('*')
      if (profiles) {
        for (const profile of profiles) {
          await firestore.collection('users').doc(profile.id).set({
            email: profile.email,
            fullName: profile.full_name,
            firstName: profile.full_name.split(' ')[0] || '',
            lastName: profile.full_name.split(' ').slice(1).join(' ') || '',
            phone: profile.phone,
            role: profile.role,
            bio: profile.bio,
            profileImageUrl: profile.profile_image_url,
            languagePreference: profile.language_preference || 'en',
            createdAt: new Date(profile.created_at),
            updatedAt: new Date(profile.updated_at)
          })
          this.stats.users++
        }
      }

      console.log(`✅ Migrated ${this.stats.users} users`)

    } catch (error) {
      console.error('❌ Error migrating users:', error)
      this.stats.errors.push(`User migration error: ${error}`)
      throw error
    }
  }

  // Migrate farms
  private async migrateFarms() {
    console.log('🏞️ Migrating farms...')

    try {
      const { data: farms } = await supabase
        .from('game_farms')
        .select(`
          *,
          farm_images(*),
          farm_amenity_links(farm_amenities(*)),
          farm_accommodations(*),
          farm_species(game_species(*))
        `)

      if (farms) {
        for (const farm of farms) {
          // Create main farm document
          await firestore.collection('farms').doc(farm.id).set({
            ownerId: farm.owner_id,
            name: farm.name,
            description: farm.description,
            descriptionAfrikaans: farm.description_afrikaans,
            location: farm.location,
            province: farm.province,
            coordinates: farm.coordinates,
            sizeHectares: farm.size_hectares,
            activityTypes: farm.activity_types,
            contactEmail: farm.contact_email,
            contactPhone: farm.contact_phone,
            websiteUrl: farm.website_url,
            rules: farm.rules,
            rulesAfrikaans: farm.rules_afrikaans,
            pricingInfo: farm.pricing_info,
            isActive: farm.is_active,
            featured: farm.featured,
            createdAt: new Date(farm.created_at),
            updatedAt: new Date(farm.updated_at)
          })

          // Migrate farm images
          if (farm.farm_images) {
            for (const image of farm.farm_images) {
              await firestore
                .collection('farms')
                .doc(farm.id)
                .collection('images')
                .doc(image.id)
                .set({
                  farmId: farm.id,
                  imageUrl: image.image_url,
                  altText: image.alt_text,
                  displayOrder: image.display_order,
                  isPrimary: image.is_primary,
                  createdAt: new Date(image.created_at)
                })
            }
          }

          this.stats.farms++
        }
      }

      console.log(`✅ Migrated ${this.stats.farms} farms`)

    } catch (error) {
      console.error('❌ Error migrating farms:', error)
      this.stats.errors.push(`Farm migration error: ${error}`)
      throw error
    }
  }

  // Print migration statistics
  private printStats() {
    console.log('\n📊 Migration Statistics:')
    console.log(`Users: ${this.stats.users}`)
    console.log(`Farms: ${this.stats.farms}`)
    console.log(`Bookings: ${this.stats.bookings}`)
    console.log(`Reviews: ${this.stats.reviews}`)
    console.log(`Species: ${this.stats.species}`)
    console.log(`Amenities: ${this.stats.amenities}`)
    console.log(`Files: ${this.stats.files}`)

    if (this.stats.errors.length > 0) {
      console.log('\n❌ Errors:')
      this.stats.errors.forEach(error => console.log(`  - ${error}`))
    }
  }

  // Migrate bookings
  private async migrateBookings() {
    console.log('📅 Migrating bookings...')

    try {
      const { data: bookings } = await supabase.from('bookings').select('*')
      if (bookings) {
        for (const booking of bookings) {
          await firestore.collection('bookings').doc(booking.id).set({
            farmId: booking.farm_id,
            hunterId: booking.hunter_id,
            startDate: booking.start_date,
            endDate: booking.end_date,
            durationDays: booking.duration_days,
            status: booking.status,
            activityType: booking.activity_type,
            groupSize: booking.group_size,
            accommodationId: booking.accommodation_id,
            specialRequests: booking.special_requests,
            totalPrice: booking.total_price,
            bookingReference: booking.booking_reference,
            confirmedAt: booking.confirmed_at ? new Date(booking.confirmed_at) : null,
            cancelledAt: booking.cancelled_at ? new Date(booking.cancelled_at) : null,
            cancellationReason: booking.cancellation_reason,
            createdAt: new Date(booking.created_at),
            updatedAt: new Date(booking.updated_at)
          })
          this.stats.bookings++
        }
      }

      console.log(`✅ Migrated ${this.stats.bookings} bookings`)

    } catch (error) {
      console.error('❌ Error migrating bookings:', error)
      this.stats.errors.push(`Booking migration error: ${error}`)
      throw error
    }
  }

  // Migrate reviews
  private async migrateReviews() {
    console.log('⭐ Migrating reviews...')

    try {
      const { data: reviews } = await supabase.from('reviews').select('*')
      if (reviews) {
        for (const review of reviews) {
          const farmRef = firestore.collection('farms').doc(review.farm_id)
          await farmRef.collection('reviews').doc(review.id).set({
            bookingId: review.booking_id,
            reviewerId: review.reviewer_id,
            farmId: review.farm_id,
            rating: review.rating,
            title: review.title,
            comment: review.comment,
            wouldRecommend: review.would_recommend,
            isPublic: review.is_public,
            responseFromOwner: review.response_from_owner,
            respondedAt: review.responded_at ? new Date(review.responded_at) : null,
            createdAt: new Date(review.created_at),
            updatedAt: new Date(review.updated_at)
          })
          this.stats.reviews++
        }
      }

      console.log(`✅ Migrated ${this.stats.reviews} reviews`)

    } catch (error) {
      console.error('❌ Error migrating reviews:', error)
      this.stats.errors.push(`Review migration error: ${error}`)
      throw error
    }
  }

  // Migrate files (placeholder)
  private async migrateFiles() {
    console.log('📁 File migration placeholder...')
    console.log('⚠️ File migration requires manual implementation')
    // TODO: Implement file migration from Supabase Storage to Firebase Storage
  }

  async run() {
    await this.migrate()
  }

}

// Run migration if called directly
if (require.main === module) {
  const migration = new FirebaseMigration()
  migration.run().catch(console.error)
}

export default FirebaseMigration
