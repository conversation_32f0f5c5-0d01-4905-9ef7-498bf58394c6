#!/usr/bin/env node

/**
 * Test script to verify the authentication flow and custom claims implementation
 * This script tests the complete registration flow including custom claims setting
 */

const admin = require('firebase-admin');
const fetch = require('node-fetch');

// Configuration
const PROJECT_ID = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID;
const SERVICE_ACCOUNT_KEY = process.env.FIREBASE_SERVICE_ACCOUNT_KEY;

if (!PROJECT_ID || !SERVICE_ACCOUNT_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_FIREBASE_PROJECT_ID');
  console.error('   - FIREBASE_SERVICE_ACCOUNT_KEY');
  process.exit(1);
}

// Initialize Firebase Admin
if (admin.apps.length === 0) {
  admin.initializeApp({
    credential: admin.credential.cert(JSON.parse(SERVICE_ACCOUNT_KEY)),
    projectId: PROJECT_ID,
  });
}

const auth = admin.auth();
const firestore = admin.firestore();

/**
 * Test user data
 */
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'testpassword123',
    role: 'farm_owner',
    firstName: 'John',
    lastName: 'Farmer'
  },
  {
    email: '<EMAIL>',
    password: 'testpassword123',
    role: 'guest',
    firstName: 'Jane',
    lastName: 'Hunter'
  }
];

/**
 * Clean up test users
 */
async function cleanupTestUsers() {
  console.log('🧹 Cleaning up test users...');
  
  for (const testUser of testUsers) {
    try {
      const userRecord = await auth.getUserByEmail(testUser.email);
      await auth.deleteUser(userRecord.uid);
      await firestore.collection('users').doc(userRecord.uid).delete();
      console.log(`   ✅ Deleted user: ${testUser.email}`);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        console.log(`   ℹ️  User not found: ${testUser.email}`);
      } else {
        console.log(`   ⚠️  Error deleting user ${testUser.email}:`, error.message);
      }
    }
  }
}

/**
 * Create a test user and verify the complete flow
 */
async function testUserRegistration(userData) {
  console.log(`\n👤 Testing registration for: ${userData.email} (${userData.role})`);
  
  try {
    // Step 1: Create user with Firebase Auth
    console.log('   1️⃣ Creating Firebase Auth user...');
    const userRecord = await auth.createUser({
      email: userData.email,
      password: userData.password,
      displayName: `${userData.firstName} ${userData.lastName}`,
    });
    console.log(`   ✅ Created user with UID: ${userRecord.uid}`);

    // Step 2: Create Firestore profile document
    console.log('   2️⃣ Creating Firestore profile...');
    const profileData = {
      email: userData.email,
      fullName: `${userData.firstName} ${userData.lastName}`,
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: userData.role,
      languagePreference: 'en',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    };
    
    await firestore.collection('users').doc(userRecord.uid).set(profileData);
    console.log('   ✅ Created Firestore profile');

    // Step 3: Set custom claims
    console.log('   3️⃣ Setting custom claims...');
    await auth.setCustomUserClaims(userRecord.uid, { role: userData.role });
    console.log(`   ✅ Set custom claims: role=${userData.role}`);

    // Step 4: Verify custom claims
    console.log('   4️⃣ Verifying custom claims...');
    const updatedUserRecord = await auth.getUser(userRecord.uid);
    const customClaims = updatedUserRecord.customClaims;
    
    if (customClaims && customClaims.role === userData.role) {
      console.log(`   ✅ Custom claims verified: role=${customClaims.role}`);
    } else {
      console.log(`   ❌ Custom claims mismatch. Expected: ${userData.role}, Got: ${customClaims?.role || 'none'}`);
      return false;
    }

    // Step 5: Test token generation and verification
    console.log('   5️⃣ Testing token generation...');
    const customToken = await auth.createCustomToken(userRecord.uid);
    console.log('   ✅ Generated custom token');

    // Step 6: Verify Firestore profile
    console.log('   6️⃣ Verifying Firestore profile...');
    const profileDoc = await firestore.collection('users').doc(userRecord.uid).get();
    
    if (profileDoc.exists) {
      const profile = profileDoc.data();
      if (profile.role === userData.role) {
        console.log(`   ✅ Firestore profile verified: role=${profile.role}`);
      } else {
        console.log(`   ❌ Firestore profile role mismatch. Expected: ${userData.role}, Got: ${profile.role}`);
        return false;
      }
    } else {
      console.log('   ❌ Firestore profile not found');
      return false;
    }

    console.log(`   🎉 Registration test PASSED for ${userData.email}`);
    return true;

  } catch (error) {
    console.log(`   ❌ Registration test FAILED for ${userData.email}:`, error.message);
    return false;
  }
}

/**
 * Test role-based access by simulating security rule checks
 */
async function testRoleBasedAccess() {
  console.log('\n🔐 Testing role-based access simulation...');
  
  try {
    // Get test users
    const farmOwnerRecord = await auth.getUserByEmail(testUsers[0].email);
    const guestRecord = await auth.getUserByEmail(testUsers[1].email);

    // Simulate security rule checks
    console.log('   📋 Simulating Firestore security rule checks...');
    
    // Test farm owner permissions
    const farmOwnerClaims = farmOwnerRecord.customClaims;
    const canFarmOwnerCreateFarm = farmOwnerClaims?.role === 'farm_owner' || farmOwnerClaims?.role === 'admin';
    console.log(`   Farm owner can create farms: ${canFarmOwnerCreateFarm ? '✅' : '❌'}`);
    
    // Test guest permissions
    const guestClaims = guestRecord.customClaims;
    const canGuestCreateBooking = guestClaims?.role === 'guest' || guestClaims?.role === 'admin';
    console.log(`   Guest can create bookings: ${canGuestCreateBooking ? '✅' : '❌'}`);
    
    // Test invalid permissions
    const canGuestCreateFarm = guestClaims?.role === 'farm_owner' || guestClaims?.role === 'admin';
    console.log(`   Guest can create farms: ${canGuestCreateFarm ? '❌ (should be false)' : '✅'}`);
    
    return canFarmOwnerCreateFarm && canGuestCreateBooking && !canGuestCreateFarm;

  } catch (error) {
    console.log('   ❌ Role-based access test failed:', error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Starting Authentication Flow Tests');
  console.log('=====================================');
  
  let allTestsPassed = true;

  try {
    // Clean up any existing test users
    await cleanupTestUsers();

    // Test user registration for each role
    for (const userData of testUsers) {
      const testPassed = await testUserRegistration(userData);
      if (!testPassed) {
        allTestsPassed = false;
      }
    }

    // Test role-based access
    const accessTestPassed = await testRoleBasedAccess();
    if (!accessTestPassed) {
      allTestsPassed = false;
    }

    // Final cleanup
    await cleanupTestUsers();

    // Results
    console.log('\n📊 Test Results');
    console.log('===============');
    
    if (allTestsPassed) {
      console.log('🎉 All tests PASSED!');
      console.log('✅ User registration flow works correctly');
      console.log('✅ Custom claims are set properly');
      console.log('✅ Role-based access control is functional');
      process.exit(0);
    } else {
      console.log('❌ Some tests FAILED!');
      console.log('⚠️  Please check the implementation and try again');
      process.exit(1);
    }

  } catch (error) {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests, cleanupTestUsers };
