"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.setUserRoleHandler = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
/**
 * Validates that the provided role is valid
 */
function isValidRole(role) {
    return role === "farm_owner" || role === "guest" || role === "admin";
}
/**
 * Validates the request body for setting user role
 */
function validateSetRoleRequest(body) {
    if (!body || typeof body !== "object") {
        throw new functions.https.HttpsError("invalid-argument", "Request body is required");
    }
    const { uid, role } = body;
    if (!uid || typeof uid !== "string") {
        throw new functions.https.HttpsError("invalid-argument", "Valid uid is required");
    }
    if (!role || typeof role !== "string" || !isValidRole(role)) {
        throw new functions.https.HttpsError("invalid-argument", "Valid role is required. Must be 'farm_owner', 'guest', or 'admin'");
    }
    return { uid, role };
}
/**
 * Validates that the requesting user has permission to set roles
 * For now, users can only set their own role during registration
 * In the future, this could be extended to allow admins to change any user's role
 */
async function validatePermissions(decodedToken, targetUid) {
    // Check if user is setting their own role (during registration)
    if (decodedToken.uid === targetUid) {
        // Allow users to set their own role if they don't have one yet
        // This handles the registration flow
        return;
    }
    // Check if user is an admin (for future admin functionality)
    if (decodedToken.role === "admin") {
        return;
    }
    throw new functions.https.HttpsError("permission-denied", "You can only set your own role during registration");
}
/**
 * Sets custom claims for a user
 */
async function setCustomClaims(uid, role) {
    try {
        await admin.auth().setCustomUserClaims(uid, { role });
        functions.logger.info(`Successfully set role '${role}' for user ${uid}`);
    }
    catch (error) {
        functions.logger.error(`Error setting custom claims for user ${uid}:`, error);
        throw new functions.https.HttpsError("internal", "Failed to set user role");
    }
}
/**
 * Verifies that the user exists in Firestore with the specified role
 */
async function verifyUserProfile(uid, role) {
    try {
        const userDoc = await admin.firestore()
            .collection("users")
            .doc(uid)
            .get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError("not-found", "User profile not found in database");
        }
        const userData = userDoc.data();
        if ((userData === null || userData === void 0 ? void 0 : userData.role) !== role) {
            throw new functions.https.HttpsError("invalid-argument", "Role mismatch between request and user profile");
        }
    }
    catch (error) {
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        functions.logger.error(`Error verifying user profile for ${uid}:`, error);
        throw new functions.https.HttpsError("internal", "Failed to verify user profile");
    }
}
/**
 * Cloud Function to set user role as custom claims
 * This function is called after user registration to set the user's role
 * as a custom claim, which is required for Firestore and Storage security rules
 */
const setUserRoleHandler = async (req, res) => {
    // Set CORS headers
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
    res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
    // Handle preflight requests
    if (req.method === "OPTIONS") {
        res.status(200).send();
        return;
    }
    // Only allow POST requests
    if (req.method !== "POST") {
        res.status(405).json({ error: "Method not allowed" });
        return;
    }
    try {
        // Verify authentication
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith("Bearer ")) {
            res.status(401).json({ error: "Unauthorized: Missing or invalid token" });
            return;
        }
        const idToken = authHeader.split("Bearer ")[1];
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        // Validate request body
        const { uid, role } = validateSetRoleRequest(req.body);
        // Validate permissions
        await validatePermissions(decodedToken, uid);
        // Verify user profile exists and has the correct role
        await verifyUserProfile(uid, role);
        // Set custom claims
        await setCustomClaims(uid, role);
        // Return success response
        res.status(200).json({
            success: true,
            message: `Role '${role}' set successfully for user ${uid}`,
            uid,
            role
        });
    }
    catch (error) {
        functions.logger.error("Error in setUserRoleHandler:", error);
        if (error instanceof functions.https.HttpsError) {
            res.status(400).json({
                error: error.message,
                code: error.code
            });
        }
        else {
            res.status(500).json({
                error: "Internal server error"
            });
        }
    }
};
exports.setUserRoleHandler = setUserRoleHandler;
//# sourceMappingURL=auth.js.map