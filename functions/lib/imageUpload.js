"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageUploadHandler = imageUploadHandler;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const busboy_1 = __importDefault(require("busboy"));
const uuid_1 = require("uuid");
const config_1 = require("./config");
const imageProcessor_1 = require("./utils/imageProcessor");
const imageValidator_1 = require("./utils/imageValidator");
const analytics_1 = require("./utils/analytics");
/**
 * Validates user authentication and authorization
 */
async function validateAuth(req, bucket, farmId, userId) {
    var _a;
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
        throw new functions.https.HttpsError("unauthenticated", "Missing or invalid authorization header");
    }
    const token = authHeader.split("Bearer ")[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    // Validate bucket-specific authorization
    if (bucket === "profile-images") {
        if (!userId || decodedToken.uid !== userId) {
            throw new functions.https.HttpsError("permission-denied", "Users can only upload to their own profile");
        }
    }
    else if (bucket === "farm-images") {
        if (!farmId) {
            throw new functions.https.HttpsError("invalid-argument", "Farm ID is required for farm image uploads");
        }
        // Check if user is farm owner or admin
        const userRole = decodedToken.role;
        if (userRole !== "farm_owner" && userRole !== "admin") {
            throw new functions.https.HttpsError("permission-denied", "Only farm owners can upload farm images");
        }
        // For farm images, verify the user owns the farm
        if (userRole !== "admin") {
            const farmDoc = await admin.firestore()
                .collection("farms")
                .doc(farmId)
                .get();
            if (!farmDoc.exists || ((_a = farmDoc.data()) === null || _a === void 0 ? void 0 : _a.ownerId) !== decodedToken.uid) {
                throw new functions.https.HttpsError("permission-denied", "You can only upload images to farms you own");
            }
        }
    }
    return decodedToken;
}
/**
 * Validates file properties
 */
function validateFile(file, metadata) {
    // Check file size
    const maxSize = config_1.CONFIG.MAX_FILE_SIZE[metadata.bucket];
    if (file.length > maxSize) {
        throw new functions.https.HttpsError("invalid-argument", `File size exceeds ${maxSize / (1024 * 1024)}MB limit`);
    }
    // Check MIME type
    if (!config_1.CONFIG.ALLOWED_MIME_TYPES.includes(metadata.contentType)) {
        throw new functions.https.HttpsError("invalid-argument", "Invalid file type. Only images are allowed.");
    }
}
/**
 * Processes and optimizes image using enhanced processor
 */
async function processImage(file, metadata) {
    try {
        // Validate MIME type against file content
        const isMimeValid = imageValidator_1.ImageValidator.validateMimeType(file, metadata.contentType);
        if (!isMimeValid) {
            throw new functions.https.HttpsError("invalid-argument", "File content does not match declared MIME type");
        }
        // Process image with enhanced processor
        const processed = await imageProcessor_1.ImageProcessor.processImage(file, {
            bucket: metadata.bucket,
            removeMetadata: true,
            generateThumbnail: false // We'll add thumbnail support later if needed
        });
        functions.logger.info("Image processed successfully", {
            originalSize: processed.metadata.originalSize,
            processedSize: processed.metadata.processedSize,
            compressionRatio: processed.metadata.compressionRatio,
            dimensions: processed.metadata.processedDimensions
        });
        return processed.main;
    }
    catch (error) {
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        functions.logger.error("Error processing image:", error);
        throw new functions.https.HttpsError("internal", "Failed to process image");
    }
}
/**
 * Uploads file to Firebase Storage
 */
async function uploadToStorage(file, metadata, onProgress) {
    const bucket = admin.storage().bucket();
    // Generate unique filename
    const timestamp = Date.now();
    const uuid = (0, uuid_1.v4)();
    const sanitizedName = metadata.originalName.replace(/[^a-zA-Z0-9.-]/g, "_");
    const fileName = `${timestamp}-${uuid}-${sanitizedName}`;
    // Determine storage path
    let storagePath;
    if (metadata.bucket === "farm-images" && metadata.farmId) {
        storagePath = `farm-images/${metadata.farmId}/${fileName}`;
    }
    else if (metadata.bucket === "profile-images" && metadata.userId) {
        storagePath = `profile-images/${metadata.userId}/${fileName}`;
    }
    else {
        throw new functions.https.HttpsError("invalid-argument", "Invalid bucket or missing required IDs");
    }
    const fileRef = bucket.file(storagePath);
    // Upload with metadata
    const uploadMetadata = {
        metadata: Object.assign(Object.assign({ contentType: metadata.contentType, originalName: metadata.originalName, uploadedAt: new Date().toISOString() }, (metadata.farmId && { farmId: metadata.farmId })), (metadata.userId && { userId: metadata.userId }))
    };
    try {
        // For progress tracking, we'll simulate progress since we can't get real progress from admin SDK
        if (onProgress) {
            onProgress({ bytesTransferred: 0, totalBytes: file.length, progress: 0 });
        }
        await fileRef.save(file, uploadMetadata);
        if (onProgress) {
            onProgress({ bytesTransferred: file.length, totalBytes: file.length, progress: 100 });
        }
        // Make file publicly readable
        await fileRef.makePublic();
        // Return public URL
        return `https://storage.googleapis.com/${bucket.name}/${storagePath}`;
    }
    catch (error) {
        functions.logger.error("Error uploading to storage:", error);
        throw new functions.https.HttpsError("internal", "Failed to upload file to storage");
    }
}
/**
 * Main image upload handler
 */
async function imageUploadHandler(req, res) {
    functions.logger.info("Image upload handler called", { method: req.method, url: req.url });
    // Set CORS headers
    const origin = req.headers.origin;
    const allowedOrigins = config_1.CONFIG.CORS.ALLOWED_ORIGINS;
    // More permissive CORS for development
    if (allowedOrigins.includes("*")) {
        res.set("Access-Control-Allow-Origin", "*");
    }
    else if (origin && allowedOrigins.includes(origin)) {
        res.set("Access-Control-Allow-Origin", origin);
    }
    else {
        // Fallback for development
        res.set("Access-Control-Allow-Origin", "*");
    }
    res.set("Access-Control-Allow-Methods", config_1.CONFIG.CORS.ALLOWED_METHODS.join(", "));
    res.set("Access-Control-Allow-Headers", config_1.CONFIG.CORS.ALLOWED_HEADERS.join(", "));
    res.set("Access-Control-Allow-Credentials", "true");
    res.set("Access-Control-Max-Age", "86400"); // Cache preflight for 24 hours
    if (req.method === "OPTIONS") {
        res.status(200).send();
        return;
    }
    // Add health check endpoint
    if (req.method === "GET" && req.url === "/health") {
        res.status(200).json({ status: "healthy", timestamp: new Date().toISOString() });
        return;
    }
    if (req.method !== "POST") {
        res.status(405).json({ error: "Method not allowed" });
        return;
    }
    try {
        // Parse multipart form data
        const bb = (0, busboy_1.default)({ headers: req.headers });
        let fileBuffer = null;
        let metadata = null;
        // Handle file upload
        bb.on("file", (_name, file, info) => {
            const { filename, mimeType } = info;
            const chunks = [];
            file.on("data", (chunk) => {
                chunks.push(chunk);
            });
            file.on("end", () => {
                fileBuffer = Buffer.concat(chunks);
                if (!metadata) {
                    metadata = {
                        bucket: "farm-images", // Default, will be overridden by form fields
                        originalName: filename,
                        contentType: mimeType,
                    };
                }
                else {
                    metadata.originalName = filename;
                    metadata.contentType = mimeType;
                }
            });
        });
        // Handle form fields
        bb.on("field", (name, value) => {
            if (!metadata) {
                metadata = {
                    bucket: "farm-images",
                    originalName: "",
                    contentType: "",
                };
            }
            if (name === "bucket") {
                metadata.bucket = value;
            }
            else if (name === "farmId") {
                metadata.farmId = value;
            }
            else if (name === "userId") {
                metadata.userId = value;
            }
        });
        // Handle completion
        bb.on("finish", async () => {
            const startTime = Date.now();
            let decodedToken = null;
            let originalFileSize = 0;
            let processedFileSize = 0;
            let success = false;
            let errorCode;
            try {
                if (!fileBuffer || !metadata) {
                    res.status(400).json({ error: "Missing file or metadata" });
                    return;
                }
                originalFileSize = fileBuffer.length;
                // Validate authentication and authorization
                decodedToken = await validateAuth(req, metadata.bucket, metadata.farmId, metadata.userId);
                // Validate file
                validateFile(fileBuffer, metadata);
                // Process image
                const processedFile = await processImage(fileBuffer, metadata);
                processedFileSize = processedFile.length;
                // Upload to storage with progress tracking
                const downloadURL = await uploadToStorage(processedFile, metadata, (progress) => {
                    // In a real implementation, you might want to use Server-Sent Events for real-time progress
                    // For now, we'll just log progress
                    functions.logger.info(`Upload progress: ${progress.progress}%`);
                });
                success = true;
                // Return success response
                res.status(200).json({
                    success: true,
                    downloadURL,
                    metadata: {
                        originalName: metadata.originalName,
                        contentType: metadata.contentType,
                        bucket: metadata.bucket,
                        size: processedFile.length,
                    }
                });
            }
            catch (error) {
                functions.logger.error("Upload error:", error);
                if (error instanceof functions.https.HttpsError) {
                    errorCode = error.code;
                    res.status(400).json({
                        error: error.message,
                        code: error.code
                    });
                }
                else {
                    errorCode = "internal";
                    res.status(500).json({
                        error: "Internal server error"
                    });
                }
            }
            finally {
                // Record analytics (don't await to avoid affecting response time)
                if (decodedToken && metadata) {
                    const processingTime = Date.now() - startTime;
                    const compressionRatio = originalFileSize > 0
                        ? ((originalFileSize - processedFileSize) / originalFileSize) * 100
                        : 0;
                    const analyticsData = {
                        timestamp: startTime,
                        userId: decodedToken.uid,
                        bucket: metadata.bucket,
                        farmId: metadata.farmId,
                        fileSize: originalFileSize,
                        processedSize: processedFileSize,
                        compressionRatio,
                        processingTime,
                        success,
                        errorCode,
                        userAgent: req.headers["user-agent"],
                        ipAddress: req.ip || req.socket.remoteAddress
                    };
                    analytics_1.UploadAnalytics.recordUpload(analyticsData).catch((analyticsError) => {
                        functions.logger.warn("Failed to record analytics:", analyticsError);
                    });
                }
            }
        });
        bb.on("error", (error) => {
            functions.logger.error("Busboy error:", error);
            res.status(400).json({ error: "Failed to parse upload" });
        });
        // Pipe request to busboy
        req.pipe(bb);
    }
    catch (error) {
        functions.logger.error("Request handling error:", error);
        res.status(500).json({ error: "Internal server error" });
    }
}
//# sourceMappingURL=imageUpload.js.map