"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockStorage = exports.mockFirestore = exports.mockAuth = void 0;
const test = __importStar(require("firebase-functions-test"));
// Initialize the test environment
const testEnv = test();
// Mock Firebase Admin
const mockAuth = {
    verifyIdToken: jest.fn(),
};
exports.mockAuth = mockAuth;
const mockFirestore = {
    collection: jest.fn(() => ({
        doc: jest.fn(() => ({
            get: jest.fn(),
        })),
    })),
};
exports.mockFirestore = mockFirestore;
const mockStorage = {
    bucket: jest.fn(() => ({
        file: jest.fn(() => ({
            save: jest.fn(),
            makePublic: jest.fn(),
        })),
    })),
};
exports.mockStorage = mockStorage;
// Mock admin SDK
jest.mock("firebase-admin", () => ({
    auth: () => mockAuth,
    firestore: () => mockFirestore,
    storage: () => mockStorage,
}));
describe("Image Upload Cloud Function", () => {
    let imageUploadHandler;
    beforeAll(() => {
        // Import the function after mocking
        const { imageUploadHandler: handler } = require("../imageUpload");
        imageUploadHandler = handler;
    });
    afterAll(() => {
        testEnv.cleanup();
    });
    beforeEach(() => {
        jest.clearAllMocks();
    });
    describe("Authentication", () => {
        it("should reject requests without authorization header", async () => {
            const req = {
                method: "POST",
                headers: {},
            };
            const res = {
                set: jest.fn(),
                status: jest.fn(() => res),
                json: jest.fn(),
            };
            await imageUploadHandler(req, res);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({
                error: "Missing or invalid authorization header",
                code: "unauthenticated",
            });
        });
        it("should reject requests with invalid token format", async () => {
            const req = {
                method: "POST",
                headers: {
                    authorization: "InvalidToken",
                },
            };
            const res = {
                set: jest.fn(),
                status: jest.fn(() => res),
                json: jest.fn(),
            };
            await imageUploadHandler(req, res);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({
                error: "Missing or invalid authorization header",
                code: "unauthenticated",
            });
        });
    });
    describe("CORS", () => {
        it("should handle OPTIONS requests", async () => {
            const req = {
                method: "OPTIONS",
                headers: {
                    origin: "http://localhost:3000",
                },
            };
            const res = {
                set: jest.fn(),
                status: jest.fn(() => res),
                send: jest.fn(),
            };
            await imageUploadHandler(req, res);
            expect(res.set).toHaveBeenCalledWith("Access-Control-Allow-Origin", "http://localhost:3000");
            expect(res.status).toHaveBeenCalledWith(200);
            expect(res.send).toHaveBeenCalled();
        });
        it("should reject non-POST methods", async () => {
            const req = {
                method: "GET",
                headers: {},
            };
            const res = {
                set: jest.fn(),
                status: jest.fn(() => res),
                json: jest.fn(),
            };
            await imageUploadHandler(req, res);
            expect(res.status).toHaveBeenCalledWith(405);
            expect(res.json).toHaveBeenCalledWith({
                error: "Method not allowed",
            });
        });
    });
    describe("File Validation", () => {
        // Note: These tests would require more complex mocking of busboy and file streams
        // For now, we'll focus on the core validation logic
        it("should validate file size limits", () => {
            const { validateFile } = require("../imageUpload");
            const largeFile = Buffer.alloc(60 * 1024 * 1024); // 60MB
            const metadata = {
                bucket: "farm-images",
                contentType: "image/jpeg",
                originalName: "test.jpg",
            };
            expect(() => validateFile(largeFile, metadata)).toThrow();
        });
        it("should validate MIME types", () => {
            const { validateFile } = require("../imageUpload");
            const file = Buffer.alloc(1024); // 1KB
            const metadata = {
                bucket: "farm-images",
                contentType: "text/plain",
                originalName: "test.txt",
            };
            expect(() => validateFile(file, metadata)).toThrow();
        });
    });
});
//# sourceMappingURL=imageUpload.test.js.map