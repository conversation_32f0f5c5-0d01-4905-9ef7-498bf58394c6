{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../src/auth.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,8DAAgD;AAChD,sDAAwC;AAWxC;;GAEG;AACH,SAAS,WAAW,CAAC,IAAY;IAC/B,OAAO,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,CAAC;AACvE,CAAC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAAC,IAAS;IACvC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtC,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,kBAAkB,EAClB,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED,MAAM,EAAC,GAAG,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC;IAEzB,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QACpC,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,kBAAkB,EAClB,uBAAuB,CACxB,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5D,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,kBAAkB,EAClB,mEAAmE,CACpE,CAAC;IACJ,CAAC;IAED,OAAO,EAAC,GAAG,EAAE,IAAI,EAAC,CAAC;AACrB,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,mBAAmB,CAChC,YAAuC,EACvC,SAAiB;IAEjB,gEAAgE;IAChE,IAAI,YAAY,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QACnC,+DAA+D;QAC/D,qCAAqC;QACrC,OAAO;IACT,CAAC;IAED,6DAA6D;IAC7D,IAAI,YAAY,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAClC,OAAO;IACT,CAAC;IAED,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,mBAAmB,EACnB,oDAAoD,CACrD,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,eAAe,CAAC,GAAW,EAAE,IAAc;IACxD,IAAI,CAAC;QACH,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE,EAAC,IAAI,EAAC,CAAC,CAAC;QACpD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,cAAc,GAAG,EAAE,CAAC,CAAC;IAC3E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9E,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,UAAU,EACV,yBAAyB,CAC1B,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,iBAAiB,CAAC,GAAW,EAAE,IAAc;IAC1D,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,SAAS,EAAE;aACpC,UAAU,CAAC,OAAO,CAAC;aACnB,GAAG,CAAC,GAAG,CAAC;aACR,GAAG,EAAE,CAAC;QAET,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,WAAW,EACX,oCAAoC,CACrC,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAChC,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,MAAK,IAAI,EAAE,CAAC;YAC5B,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,kBAAkB,EAClB,gDAAgD,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;QACD,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;QAC1E,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,UAAU,EACV,+BAA+B,CAChC,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;;GAIG;AACI,MAAM,kBAAkB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACtE,mBAAmB;IACnB,GAAG,CAAC,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;IAC5C,GAAG,CAAC,GAAG,CAAC,8BAA8B,EAAE,eAAe,CAAC,CAAC;IACzD,GAAG,CAAC,GAAG,CAAC,8BAA8B,EAAE,6BAA6B,CAAC,CAAC;IAEvE,4BAA4B;IAC5B,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QAC7B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QACvB,OAAO;IACT,CAAC;IAED,2BAA2B;IAC3B,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC1B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,oBAAoB,EAAC,CAAC,CAAC;QACpD,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,wBAAwB;QACxB,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,wCAAwC,EAAC,CAAC,CAAC;YACxE,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAE/D,wBAAwB;QACxB,MAAM,EAAC,GAAG,EAAE,IAAI,EAAC,GAAG,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAErD,uBAAuB;QACvB,MAAM,mBAAmB,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAE7C,sDAAsD;QACtD,MAAM,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAEnC,oBAAoB;QACpB,MAAM,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAEjC,0BAA0B;QAC1B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,SAAS,IAAI,+BAA+B,GAAG,EAAE;YAC1D,GAAG;YACH,IAAI;SACL,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QAE9D,IAAI,KAAK,YAAY,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAChD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;aACjB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AA/DW,QAAA,kBAAkB,sBA+D7B"}