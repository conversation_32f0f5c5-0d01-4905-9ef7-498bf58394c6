{"version": 3, "file": "analytics.js", "sourceRoot": "", "sources": ["../../src/utils/analytics.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,8DAAgD;AAChD,sDAAwC;AA6BxC;;GAEG;AACH,MAAa,eAAe;IAI1B;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAsB;QAC9C,IAAI,CAAC;YACH,MAAM,KAAK,CAAC,SAAS,EAAE;iBACpB,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC;iBAC3C,GAAG,iCACC,OAAO,KACV,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE,IACvD,CAAC;YAEL,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBAC/C,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,cAAc,EAAE,OAAO,CAAC,cAAc;aACvC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAClE,4DAA4D;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAChC,SAAe,EACf,OAAa;QAEb,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE;iBAC5B,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC;iBAC3C,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;iBAC7C,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC;iBAC3C,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,iCAAiC;YAElD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAmB,CAAC,CAAC;YAEtE,OAAO,eAAe,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YACpE,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,UAAU,EACV,wCAAwC,CACzC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,OAAwB;QACtD,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAChE,MAAM,aAAa,GAAG,YAAY,GAAG,iBAAiB,CAAC;QAEvD,iDAAiD;QACjD,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtD,MAAM,eAAe,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC;YAC/C,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM;YAChF,CAAC,CAAC,CAAC,CAAC;QAEN,MAAM,qBAAqB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC;YACrD,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM;YACtF,CAAC,CAAC,CAAC,CAAC;QAEN,MAAM,uBAAuB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC;YACvD,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM;YACxF,CAAC,CAAC,CAAC,CAAC;QAEN,oBAAoB;QACpB,MAAM,WAAW,GAA2B,EAAE,CAAC;QAC/C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACzD,WAAW,CAAC,CAAC,CAAC,SAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,SAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC;aAC9C,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;aACzC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhB,0BAA0B;QAC1B,MAAM,eAAe,GAA2B,EAAE,CAAC;QACnD,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAClB,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,wBAAwB;QACxB,MAAM,aAAa,GAA2B,EAAE,CAAC;QACjD,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAClB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC1E,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,YAAY;YACZ,iBAAiB;YACjB,aAAa;YACb,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;YAC5C,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;YACxD,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,GAAG,CAAC,GAAG,GAAG;YACxE,aAAa;YACb,eAAe;YACf,aAAa;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAC7B,MAAc,EACd,OAAe,EAAE;QAQjB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YAE9C,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE;iBAC5B,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC;iBAC3C,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;iBAC7B,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;iBAC7C,KAAK,CAAC,IAAI,CAAC,CAAC;YAEf,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAmB,CAAC,CAAC;YAEtE,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;YACpC,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YAChE,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAEtD,MAAM,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACjF,MAAM,kBAAkB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YACvF,MAAM,cAAc,GAAG,iBAAiB,GAAG,kBAAkB,CAAC;YAE9D,MAAM,uBAAuB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC;gBACvD,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM;gBACxF,CAAC,CAAC,CAAC,CAAC;YAEN,OAAO;gBACL,YAAY;gBACZ,iBAAiB;gBACjB,iBAAiB;gBACjB,cAAc;gBACd,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,GAAG,CAAC,GAAG,GAAG;aACzE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,UAAU,EACV,oCAAoC,CACrC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,aAAqB,EAAE;QACjD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,CAAC;YAEtD,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE;iBAC5B,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC;iBAC3C,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,UAAU,CAAC,OAAO,EAAE,CAAC;iBAC7C,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YAErC,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,OAAO,GAAG,IAAI,CAAC;YAEnB,OAAO,OAAO,EAAE,CAAC;gBACf,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC;gBAEnC,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;oBACnB,OAAO,GAAG,KAAK,CAAC;oBAChB,MAAM;gBACR,CAAC;gBAED,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC;gBACxC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC1B,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC;gBAEH,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;gBACrB,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBAErC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,IAAI,CAAC,MAAM,wBAAwB,CAAC,CAAC;gBAE/E,kDAAkD;gBAClD,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,UAAU,EAAE,CAAC;oBACtD,OAAO,GAAG,KAAK,CAAC;gBAClB,CAAC;YACH,CAAC;YAED,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,YAAY,WAAW,CAAC,CAAC;YACvF,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,IAAU;QAC1C,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QAChC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;QAEnC,OAAO,MAAM,eAAe,CAAC,qBAAqB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,sBAAsB,CACjC,OAA2B;QAE3B,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,qBAAqB;QACrB,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC;QAC7E,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,qBAAqB,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9D,CAAC;QAED,gCAAgC;QAChC,IAAI,OAAO,CAAC,qBAAqB,GAAG,KAAK,EAAE,CAAC,CAAC,aAAa;YACxD,MAAM,CAAC,IAAI,CAAC,yBAAyB,OAAO,CAAC,qBAAqB,IAAI,CAAC,CAAC;QAC1E,CAAC;QAED,6BAA6B;QAC7B,IAAI,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,oBAAoB,OAAO,CAAC,aAAa,oBAAoB,OAAO,CAAC,YAAY,UAAU,CAAC,CAAC;QAC3G,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;;AA/PH,0CAgQC;AA/PyB,+BAAe,GAAG,kBAAkB,CAAC;AACrC,0BAAU,GAAG,GAAG,CAAC"}