"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadAnalytics = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
/**
 * Analytics and monitoring utility for image uploads
 */
class UploadAnalytics {
    /**
     * Record upload metrics
     */
    static async recordUpload(metrics) {
        try {
            await admin.firestore()
                .collection(UploadAnalytics.COLLECTION_NAME)
                .add(Object.assign(Object.assign({}, metrics), { createdAt: admin.firestore.FieldValue.serverTimestamp() }));
            functions.logger.info("Upload metrics recorded", {
                userId: metrics.userId,
                bucket: metrics.bucket,
                success: metrics.success,
                fileSize: metrics.fileSize,
                processingTime: metrics.processingTime
            });
        }
        catch (error) {
            functions.logger.error("Failed to record upload metrics:", error);
            // Don't throw error to avoid affecting upload functionality
        }
    }
    /**
     * Get performance metrics for a time period
     */
    static async getPerformanceMetrics(startDate, endDate) {
        try {
            const query = admin.firestore()
                .collection(UploadAnalytics.COLLECTION_NAME)
                .where("timestamp", ">=", startDate.getTime())
                .where("timestamp", "<=", endDate.getTime())
                .limit(10000); // Limit to prevent memory issues
            const snapshot = await query.get();
            const uploads = snapshot.docs.map(doc => doc.data());
            return UploadAnalytics.calculateMetrics(uploads);
        }
        catch (error) {
            functions.logger.error("Failed to get performance metrics:", error);
            throw new functions.https.HttpsError("internal", "Failed to retrieve performance metrics");
        }
    }
    /**
     * Calculate metrics from upload data
     */
    static calculateMetrics(uploads) {
        const totalUploads = uploads.length;
        const successfulUploads = uploads.filter(u => u.success).length;
        const failedUploads = totalUploads - successfulUploads;
        // Calculate averages for successful uploads only
        const successfulData = uploads.filter(u => u.success);
        const averageFileSize = successfulData.length > 0
            ? successfulData.reduce((sum, u) => sum + u.fileSize, 0) / successfulData.length
            : 0;
        const averageProcessingTime = successfulData.length > 0
            ? successfulData.reduce((sum, u) => sum + u.processingTime, 0) / successfulData.length
            : 0;
        const averageCompressionRatio = successfulData.length > 0
            ? successfulData.reduce((sum, u) => sum + u.compressionRatio, 0) / successfulData.length
            : 0;
        // Count error codes
        const errorCounts = {};
        uploads.filter(u => !u.success && u.errorCode).forEach(u => {
            errorCounts[u.errorCode] = (errorCounts[u.errorCode] || 0) + 1;
        });
        const topErrorCodes = Object.entries(errorCounts)
            .map(([code, count]) => ({ code, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        // Count uploads by bucket
        const uploadsByBucket = {};
        uploads.forEach(u => {
            uploadsByBucket[u.bucket] = (uploadsByBucket[u.bucket] || 0) + 1;
        });
        // Count uploads by hour
        const uploadsByHour = {};
        uploads.forEach(u => {
            const hour = new Date(u.timestamp).getHours().toString().padStart(2, "0");
            uploadsByHour[hour] = (uploadsByHour[hour] || 0) + 1;
        });
        return {
            totalUploads,
            successfulUploads,
            failedUploads,
            averageFileSize: Math.round(averageFileSize),
            averageProcessingTime: Math.round(averageProcessingTime),
            averageCompressionRatio: Math.round(averageCompressionRatio * 100) / 100,
            topErrorCodes,
            uploadsByBucket,
            uploadsByHour
        };
    }
    /**
     * Get user-specific upload statistics
     */
    static async getUserUploadStats(userId, days = 30) {
        try {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            const query = admin.firestore()
                .collection(UploadAnalytics.COLLECTION_NAME)
                .where("userId", "==", userId)
                .where("timestamp", ">=", startDate.getTime())
                .limit(1000);
            const snapshot = await query.get();
            const uploads = snapshot.docs.map(doc => doc.data());
            const totalUploads = uploads.length;
            const successfulUploads = uploads.filter(u => u.success).length;
            const successfulData = uploads.filter(u => u.success);
            const totalDataUploaded = successfulData.reduce((sum, u) => sum + u.fileSize, 0);
            const totalProcessedData = successfulData.reduce((sum, u) => sum + u.processedSize, 0);
            const totalDataSaved = totalDataUploaded - totalProcessedData;
            const averageCompressionRatio = successfulData.length > 0
                ? successfulData.reduce((sum, u) => sum + u.compressionRatio, 0) / successfulData.length
                : 0;
            return {
                totalUploads,
                successfulUploads,
                totalDataUploaded,
                totalDataSaved,
                averageCompressionRatio: Math.round(averageCompressionRatio * 100) / 100
            };
        }
        catch (error) {
            functions.logger.error("Failed to get user upload stats:", error);
            throw new functions.https.HttpsError("internal", "Failed to retrieve user statistics");
        }
    }
    /**
     * Clean up old analytics data
     */
    static async cleanupOldData(daysToKeep = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
            const query = admin.firestore()
                .collection(UploadAnalytics.COLLECTION_NAME)
                .where("timestamp", "<", cutoffDate.getTime())
                .limit(UploadAnalytics.BATCH_SIZE);
            let deletedCount = 0;
            let hasMore = true;
            while (hasMore) {
                const snapshot = await query.get();
                if (snapshot.empty) {
                    hasMore = false;
                    break;
                }
                const batch = admin.firestore().batch();
                snapshot.docs.forEach(doc => {
                    batch.delete(doc.ref);
                });
                await batch.commit();
                deletedCount += snapshot.docs.length;
                functions.logger.info(`Deleted ${snapshot.docs.length} old analytics records`);
                // If we got fewer than the batch size, we're done
                if (snapshot.docs.length < UploadAnalytics.BATCH_SIZE) {
                    hasMore = false;
                }
            }
            functions.logger.info(`Analytics cleanup completed. Deleted ${deletedCount} records.`);
            return deletedCount;
        }
        catch (error) {
            functions.logger.error("Failed to cleanup old analytics data:", error);
            throw error;
        }
    }
    /**
     * Generate daily summary report
     */
    static async generateDailySummary(date) {
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);
        return await UploadAnalytics.getPerformanceMetrics(startOfDay, endOfDay);
    }
    /**
     * Alert on performance issues
     */
    static async checkPerformanceAlerts(metrics) {
        const alerts = [];
        // Check success rate
        const successRate = (metrics.successfulUploads / metrics.totalUploads) * 100;
        if (successRate < 95) {
            alerts.push(`Low success rate: ${successRate.toFixed(1)}%`);
        }
        // Check average processing time
        if (metrics.averageProcessingTime > 10000) { // 10 seconds
            alerts.push(`High processing time: ${metrics.averageProcessingTime}ms`);
        }
        // Check for high error rates
        if (metrics.failedUploads > metrics.totalUploads * 0.1) {
            alerts.push(`High error rate: ${metrics.failedUploads} failures out of ${metrics.totalUploads} uploads`);
        }
        return alerts;
    }
}
exports.UploadAnalytics = UploadAnalytics;
UploadAnalytics.COLLECTION_NAME = "upload_analytics";
UploadAnalytics.BATCH_SIZE = 500;
//# sourceMappingURL=analytics.js.map