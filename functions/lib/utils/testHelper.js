"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestHelper = void 0;
const admin = __importStar(require("firebase-admin"));
const functions = __importStar(require("firebase-functions"));
/**
 * Test helper utilities for Cloud Functions
 */
class TestHelper {
    /**
     * Create a test image buffer for testing
     */
    static createTestImageBuffer(width = 100, height = 100, format = "jpeg") {
        // Create a simple test image buffer
        // This is a minimal implementation - in real testing you'd use a proper image library
        const header = format === "jpeg"
            ? Buffer.from([0xFF, 0xD8, 0xFF, 0xE0]) // JPEG header
            : Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]); // PNG header
        const data = Buffer.alloc(width * height * 3); // RGB data
        return Buffer.concat([header, data]);
    }
    /**
     * Create test form data for upload testing
     */
    static createTestFormData(file, bucket, farmId, userId) {
        const formData = new FormData();
        const blob = new Blob([file], { type: "image/jpeg" });
        formData.append("file", blob, "test-image.jpg");
        formData.append("bucket", bucket);
        if (farmId) {
            formData.append("farmId", farmId);
        }
        if (userId) {
            formData.append("userId", userId);
        }
        return formData;
    }
    /**
     * Create a mock Firebase Auth token for testing
     */
    static async createTestToken(uid, role = "guest", customClaims = {}) {
        try {
            // Set custom claims
            await admin.auth().setCustomUserClaims(uid, Object.assign({ role }, customClaims));
            // Create custom token
            return await admin.auth().createCustomToken(uid);
        }
        catch (error) {
            functions.logger.error("Error creating test token:", error);
            throw error;
        }
    }
    /**
     * Create test user for testing
     */
    static async createTestUser(email, role = "guest") {
        try {
            // Create user
            const userRecord = await admin.auth().createUser({
                email,
                password: "testpassword123",
                emailVerified: true
            });
            // Create token with role
            const token = await TestHelper.createTestToken(userRecord.uid, role);
            return {
                uid: userRecord.uid,
                token
            };
        }
        catch (error) {
            functions.logger.error("Error creating test user:", error);
            throw error;
        }
    }
    /**
     * Create test farm document
     */
    static async createTestFarm(ownerId, farmData = {}) {
        try {
            const farmRef = admin.firestore().collection("farms").doc();
            await farmRef.set(Object.assign({ name: "Test Farm", ownerId, location: "Test Location", province: "Western Cape", activityTypes: "both", isActive: true, featured: false, createdAt: admin.firestore.FieldValue.serverTimestamp(), updatedAt: admin.firestore.FieldValue.serverTimestamp() }, farmData));
            return farmRef.id;
        }
        catch (error) {
            functions.logger.error("Error creating test farm:", error);
            throw error;
        }
    }
    /**
     * Clean up test data
     */
    static async cleanupTestData(userIds = [], farmIds = []) {
        try {
            // Delete test users
            for (const uid of userIds) {
                try {
                    await admin.auth().deleteUser(uid);
                }
                catch (error) {
                    functions.logger.warn(`Failed to delete user ${uid}:`, error);
                }
            }
            // Delete test farms
            for (const farmId of farmIds) {
                try {
                    await admin.firestore().collection("farms").doc(farmId).delete();
                }
                catch (error) {
                    functions.logger.warn(`Failed to delete farm ${farmId}:`, error);
                }
            }
            functions.logger.info("Test data cleanup completed");
        }
        catch (error) {
            functions.logger.error("Error during cleanup:", error);
        }
    }
    /**
     * Validate upload response
     */
    static validateUploadResponse(response) {
        return (response &&
            typeof response === "object" &&
            response.success === true &&
            typeof response.downloadURL === "string" &&
            response.downloadURL.startsWith("https://") &&
            response.metadata &&
            typeof response.metadata.originalName === "string" &&
            typeof response.metadata.contentType === "string" &&
            typeof response.metadata.bucket === "string" &&
            typeof response.metadata.size === "number");
    }
    /**
     * Generate test scenarios for comprehensive testing
     */
    static getTestScenarios() {
        return [
            {
                name: "farm_owner_upload_farm_image",
                description: "Farm owner uploads image to their farm",
                setup: async () => {
                    const user = await TestHelper.createTestUser("<EMAIL>", "farm_owner");
                    const farmId = await TestHelper.createTestFarm(user.uid);
                    return { user, farmId };
                },
                test: async ({ user, farmId }) => {
                    // const _imageBuffer = TestHelper.createTestImageBuffer(500, 500);
                    // const _formData = TestHelper.createTestFormData(_imageBuffer, "farm-images", farmId);
                    // This would be the actual HTTP request to the function
                    // Implementation depends on your testing framework
                    // TODO: Implement actual HTTP request using formData
                    console.log(`Test placeholder for user ${user.uid} and farm ${farmId}`);
                    return true; // Placeholder
                },
                cleanup: async ({ user, farmId }) => {
                    await TestHelper.cleanupTestData([user.uid], [farmId]);
                }
            },
            {
                name: "user_upload_profile_image",
                description: "Regular user uploads profile image",
                setup: async () => {
                    const user = await TestHelper.createTestUser("<EMAIL>", "guest");
                    return { user };
                },
                test: async ({ user }) => {
                    // const _imageBuffer = TestHelper.createTestImageBuffer(200, 200);
                    // const _formData = TestHelper.createTestFormData(_imageBuffer, "profile-images", undefined, user.uid);
                    // This would be the actual HTTP request to the function
                    console.log(`Test placeholder for user ${user.uid} profile upload`);
                    return true; // Placeholder
                },
                cleanup: async ({ user }) => {
                    await TestHelper.cleanupTestData([user.uid]);
                }
            },
            {
                name: "unauthorized_farm_upload",
                description: "User tries to upload to farm they don't own",
                setup: async () => {
                    const owner = await TestHelper.createTestUser("<EMAIL>", "farm_owner");
                    const user = await TestHelper.createTestUser("<EMAIL>", "guest");
                    const farmId = await TestHelper.createTestFarm(owner.uid);
                    return { owner, user, farmId };
                },
                test: async ({ user, farmId }) => {
                    // const _imageBuffer = TestHelper.createTestImageBuffer(200, 200);
                    // const _formData = TestHelper.createTestFormData(_imageBuffer, "farm-images", farmId);
                    // This should fail with permission denied
                    // Implementation depends on your testing framework
                    console.log(`Test placeholder for unauthorized user ${user.uid} trying to access farm ${farmId}`);
                    return true; // Placeholder - should return false for unauthorized access
                },
                cleanup: async ({ owner, user, farmId }) => {
                    await TestHelper.cleanupTestData([owner.uid, user.uid], [farmId]);
                }
            }
        ];
    }
    /**
     * Performance testing helper
     */
    static async measureUploadPerformance(uploadFunction, iterations = 5) {
        const times = [];
        let successes = 0;
        for (let i = 0; i < iterations; i++) {
            const startTime = Date.now();
            try {
                await uploadFunction();
                const endTime = Date.now();
                times.push(endTime - startTime);
                successes++;
            }
            catch (error) {
                functions.logger.warn(`Upload iteration ${i + 1} failed:`, error);
            }
        }
        return {
            averageTime: times.reduce((a, b) => a + b, 0) / times.length,
            minTime: Math.min(...times),
            maxTime: Math.max(...times),
            successRate: (successes / iterations) * 100
        };
    }
}
exports.TestHelper = TestHelper;
//# sourceMappingURL=testHelper.js.map