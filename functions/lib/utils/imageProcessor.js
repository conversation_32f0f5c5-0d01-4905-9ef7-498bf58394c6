"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageProcessor = void 0;
const functions = __importStar(require("firebase-functions"));
const sharp_1 = __importDefault(require("sharp"));
const config_1 = require("../config");
const imageValidator_1 = require("./imageValidator");
/**
 * Advanced image processing utility
 */
class ImageProcessor {
    /**
     * Process image with comprehensive validation and optimization
     */
    static async processImage(buffer, options) {
        try {
            // Validate image first
            const validation = await imageValidator_1.ImageValidator.validateImage(buffer, config_1.CONFIG.MAX_FILE_SIZE[options.bucket]);
            if (!validation.isValid) {
                throw new functions.https.HttpsError("invalid-argument", validation.error || "Image validation failed");
            }
            const originalMetadata = validation.metadata;
            // Sanitize image (remove metadata for security)
            let processedBuffer = buffer;
            if (options.removeMetadata !== false) {
                processedBuffer = await imageValidator_1.ImageValidator.sanitizeImage(buffer);
            }
            // Determine processing parameters
            const maxDimension = options.maxDimension || config_1.CONFIG.MAX_DIMENSIONS[options.bucket];
            const quality = options.quality || config_1.CONFIG.IMAGE_QUALITY;
            const targetFormat = options.format || ImageProcessor.determineOptimalFormat(originalMetadata);
            // Process main image
            const mainImage = await ImageProcessor.processMainImage(processedBuffer, maxDimension, quality, targetFormat);
            // Generate thumbnail if requested
            let thumbnail;
            if (options.generateThumbnail) {
                const thumbnailSize = options.thumbnailSize || 150;
                thumbnail = await ImageProcessor.generateThumbnail(processedBuffer, thumbnailSize, quality, targetFormat);
            }
            // Get processed image metadata
            const processedMetadata = await (0, sharp_1.default)(mainImage.buffer).metadata();
            return {
                main: mainImage.buffer,
                thumbnail,
                metadata: {
                    originalSize: buffer.length,
                    processedSize: mainImage.buffer.length,
                    originalDimensions: {
                        width: originalMetadata.width,
                        height: originalMetadata.height
                    },
                    processedDimensions: {
                        width: processedMetadata.width || 0,
                        height: processedMetadata.height || 0
                    },
                    format: targetFormat,
                    compressionRatio: Math.round((1 - mainImage.buffer.length / buffer.length) * 100)
                }
            };
        }
        catch (error) {
            if (error instanceof functions.https.HttpsError) {
                throw error;
            }
            functions.logger.error("Image processing error:", error);
            throw new functions.https.HttpsError("internal", "Failed to process image");
        }
    }
    /**
     * Process main image with resizing and optimization
     */
    static async processMainImage(buffer, maxDimension, quality, format) {
        let processor = (0, sharp_1.default)(buffer);
        // Get original dimensions
        const metadata = await processor.metadata();
        // Resize if necessary
        if (metadata.width && metadata.height) {
            const maxCurrentDimension = Math.max(metadata.width, metadata.height);
            if (maxCurrentDimension > maxDimension) {
                processor = processor.resize(maxDimension, maxDimension, {
                    fit: "inside",
                    withoutEnlargement: true
                });
            }
        }
        // Apply format-specific optimization
        switch (format) {
            case "jpeg":
                processor = processor.jpeg({
                    quality,
                    progressive: true,
                    mozjpeg: true // Use mozjpeg encoder for better compression
                });
                break;
            case "png":
                processor = processor.png({
                    quality,
                    compressionLevel: 9,
                    progressive: true
                });
                break;
            case "webp":
                processor = processor.webp({
                    quality,
                    effort: 6 // Higher effort for better compression
                });
                break;
        }
        const processedBuffer = await processor.toBuffer();
        return { buffer: processedBuffer };
    }
    /**
     * Generate thumbnail image
     */
    static async generateThumbnail(buffer, size, quality, format) {
        let processor = (0, sharp_1.default)(buffer)
            .resize(size, size, {
            fit: "cover",
            position: "center"
        });
        // Apply format-specific settings for thumbnail
        switch (format) {
            case "jpeg":
                processor = processor.jpeg({ quality: Math.max(quality - 10, 60) });
                break;
            case "png":
                processor = processor.png({ quality: Math.max(quality - 10, 60) });
                break;
            case "webp":
                processor = processor.webp({ quality: Math.max(quality - 10, 60) });
                break;
        }
        return await processor.toBuffer();
    }
    /**
     * Determine optimal output format based on input
     */
    static determineOptimalFormat(metadata) {
        // Keep PNG for images with transparency
        if (metadata.hasAlpha && metadata.format === "png") {
            return "png";
        }
        // Use WebP for modern browsers (when supported)
        // For now, default to JPEG for maximum compatibility
        return "jpeg";
    }
    /**
     * Estimate processing time based on image size and operations
     */
    static estimateProcessingTime(fileSize, operations) {
        // Base time: 100ms per MB
        let estimatedTime = (fileSize / (1024 * 1024)) * 100;
        // Add time for specific operations
        operations.forEach(op => {
            switch (op) {
                case "resize":
                    estimatedTime += 50;
                    break;
                case "thumbnail":
                    estimatedTime += 30;
                    break;
                case "format_conversion":
                    estimatedTime += 40;
                    break;
                case "metadata_removal":
                    estimatedTime += 10;
                    break;
            }
        });
        // Minimum 200ms, maximum 30 seconds
        return Math.max(200, Math.min(estimatedTime, 30000));
    }
    /**
     * Validate processing options
     */
    static validateProcessingOptions(options) {
        if (options.maxDimension && (options.maxDimension < 32 || options.maxDimension > 8192)) {
            throw new functions.https.HttpsError("invalid-argument", "Max dimension must be between 32 and 8192 pixels");
        }
        if (options.quality && (options.quality < 1 || options.quality > 100)) {
            throw new functions.https.HttpsError("invalid-argument", "Quality must be between 1 and 100");
        }
        if (options.thumbnailSize && (options.thumbnailSize < 16 || options.thumbnailSize > 512)) {
            throw new functions.https.HttpsError("invalid-argument", "Thumbnail size must be between 16 and 512 pixels");
        }
    }
}
exports.ImageProcessor = ImageProcessor;
//# sourceMappingURL=imageProcessor.js.map