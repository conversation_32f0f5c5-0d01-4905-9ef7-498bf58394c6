{"name": "bvr-safaris-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for BVR Safaris", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "engines": {"node": ">=20"}, "dependencies": {"firebase-admin": "^13.4.0", "firebase-functions": "^6.1.1", "busboy": "^1.6.0", "sharp": "^0.33.5", "uuid": "^11.0.3"}, "devDependencies": {"@types/busboy": "^1.5.4", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.15.0", "eslint-plugin-import": "^2.31.0", "firebase-functions-test": "^3.3.0", "typescript": "^5.7.2"}, "private": true}