# BVR Safaris Firebase Cloud Functions

This directory contains Firebase Cloud Functions for the BVR Safaris application, specifically for handling secure image uploads with server-side processing and validation.

## Features

- **Secure Image Upload**: Server-side authentication and authorization
- **Image Processing**: Automatic resizing, optimization, and format conversion using Sharp
- **File Validation**: Size limits, MIME type checking, and security validation
- **Progress Tracking**: Simulated progress updates for better UX
- **Role-based Access**: Farm owners can upload farm images, users can upload profile images
- **Error Handling**: Comprehensive error handling with detailed error messages

## Setup

### 1. Install Dependencies

```bash
cd functions
npm install
```

### 2. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your Firebase project settings:

```env
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
```

### 3. Firebase Configuration

Set Firebase configuration for the functions (optional, uses defaults if not set):

```bash
# Set image processing configuration
firebase functions:config:set images.max_farm_size_mb=50
firebase functions:config:set images.max_profile_size_mb=10
firebase functions:config:set images.quality=85
firebase functions:config:set images.max_farm_dimension=2048
firebase functions:config:set images.max_profile_dimension=512
```

## Development

### Local Development with Emulators

1. Start the Firebase emulators:

```bash
npm run serve
```

This will start the Functions emulator on `http://localhost:5001`

2. The upload endpoint will be available at:
```
http://localhost:5001/your-project-id/us-central1/uploadImage
```

### Building

```bash
npm run build
```

### Linting

```bash
npm run lint
npm run lint:fix
```

## Deployment

### Deploy Functions

```bash
npm run deploy
```

Or deploy from the root project:

```bash
cd apps/web
npm run functions:deploy
```

### Production URL

After deployment, the function will be available at:
```
https://us-central1-your-project-id.cloudfunctions.net/uploadImage
```

## API Usage

### Upload Farm Image

```javascript
const formData = new FormData()
formData.append('file', imageFile)
formData.append('bucket', 'farm-images')
formData.append('farmId', 'your-farm-id')

const response = await fetch(functionURL, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${userToken}`
  },
  body: formData
})
```

### Upload Profile Image

```javascript
const formData = new FormData()
formData.append('file', imageFile)
formData.append('bucket', 'profile-images')
formData.append('userId', 'your-user-id')

const response = await fetch(functionURL, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${userToken}`
  },
  body: formData
})
```

### Response Format

Success response:
```json
{
  "success": true,
  "downloadURL": "https://storage.googleapis.com/...",
  "metadata": {
    "originalName": "image.jpg",
    "contentType": "image/jpeg",
    "bucket": "farm-images",
    "size": 1234567
  }
}
```

Error response:
```json
{
  "error": "Error message",
  "code": "error-code"
}
```

## Security

- **Authentication**: Requires valid Firebase Auth token
- **Authorization**: Role-based access control
- **File Validation**: Size limits and MIME type checking
- **Path Validation**: Prevents directory traversal attacks
- **CORS**: Configurable CORS settings

## File Processing

- **Automatic Resizing**: Images are resized to maximum dimensions
- **Format Optimization**: JPEG compression with configurable quality
- **Metadata Preservation**: Original filename and upload metadata stored
- **Unique Naming**: Timestamp and UUID-based filename generation

## Monitoring

View function logs:

```bash
npm run logs
```

Or use the Firebase Console for detailed monitoring and error tracking.

## Troubleshooting

### Common Issues

1. **CORS Errors**: Check CORS configuration in `src/config.ts`
2. **Authentication Errors**: Verify Firebase Auth token is valid
3. **File Size Errors**: Check file size limits in configuration
4. **Permission Errors**: Verify user roles and farm ownership

### Debug Mode

Enable debug logging by setting the log level:

```bash
firebase functions:config:set debug.enabled=true
```
