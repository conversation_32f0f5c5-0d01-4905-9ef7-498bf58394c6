import * as functions from "firebase-functions";
import sharp from "sharp";

export interface ImageValidationResult {
  isValid: boolean;
  error?: string;
  metadata?: {
    width: number;
    height: number;
    format: string;
    size: number;
    hasAlpha: boolean;
  };
}

/**
 * Comprehensive image validation utility
 */
export class ImageValidator {
  private static readonly ALLOWED_FORMATS = ["jpeg", "jpg", "png", "webp", "gif"];
  private static readonly MAX_DIMENSIONS = 8192; // 8K max dimension
  private static readonly MIN_DIMENSIONS = 32; // Minimum 32px

  /**
   * Validates image file comprehensively
   */
  static async validateImage(
    buffer: Buffer,
    maxSizeBytes: number,
    allowedFormats: string[] = ImageValidator.ALLOWED_FORMATS
  ): Promise<ImageValidationResult> {
    try {
      // Basic size check
      if (buffer.length > maxSizeBytes) {
        return {
          isValid: false,
          error: `File size ${buffer.length} bytes exceeds maximum ${maxSizeBytes} bytes`
        };
      }

      // Try to parse image with Sharp
      let image: sharp.Sharp;
      try {
        image = sharp(buffer);
      } catch (error) {
        return {
          isValid: false,
          error: "Invalid image file or corrupted data"
        };
      }

      // Get image metadata
      const metadata = await image.metadata();

      if (!metadata.width || !metadata.height || !metadata.format) {
        return {
          isValid: false,
          error: "Unable to read image metadata"
        };
      }

      // Validate format
      if (!allowedFormats.includes(metadata.format.toLowerCase())) {
        return {
          isValid: false,
          error: `Unsupported format: ${metadata.format}. Allowed: ${allowedFormats.join(", ")}`
        };
      }

      // Validate dimensions
      if (metadata.width > ImageValidator.MAX_DIMENSIONS || 
          metadata.height > ImageValidator.MAX_DIMENSIONS) {
        return {
          isValid: false,
          error: `Image dimensions ${metadata.width}x${metadata.height} exceed maximum ${ImageValidator.MAX_DIMENSIONS}px`
        };
      }

      if (metadata.width < ImageValidator.MIN_DIMENSIONS || 
          metadata.height < ImageValidator.MIN_DIMENSIONS) {
        return {
          isValid: false,
          error: `Image dimensions ${metadata.width}x${metadata.height} below minimum ${ImageValidator.MIN_DIMENSIONS}px`
        };
      }

      // Check for potential security issues
      if (metadata.density && metadata.density > 1000) {
        return {
          isValid: false,
          error: "Image density too high, potential security risk"
        };
      }

      // Additional security checks for animated images
      if (metadata.pages && metadata.pages > 100) {
        return {
          isValid: false,
          error: "Too many frames in animated image"
        };
      }

      return {
        isValid: true,
        metadata: {
          width: metadata.width,
          height: metadata.height,
          format: metadata.format,
          size: buffer.length,
          hasAlpha: metadata.hasAlpha || false
        }
      };

    } catch (error) {
      functions.logger.error("Image validation error:", error);
      return {
        isValid: false,
        error: "Failed to validate image"
      };
    }
  }

  /**
   * Validates MIME type against file content
   */
  static validateMimeType(buffer: Buffer, declaredMimeType: string): boolean {
    try {
      // Check file signatures (magic numbers)
      const signatures: Record<string, number[][]> = {
        "image/jpeg": [[0xFF, 0xD8, 0xFF]],
        "image/png": [[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]],
        "image/gif": [[0x47, 0x49, 0x46, 0x38, 0x37, 0x61], [0x47, 0x49, 0x46, 0x38, 0x39, 0x61]],
        "image/webp": [[0x52, 0x49, 0x46, 0x46]] // RIFF header, need to check WEBP at offset 8
      };

      const signature = signatures[declaredMimeType.toLowerCase()];
      if (!signature) {
        return false; // Unknown MIME type
      }

      // Check if any signature matches
      return signature.some(sig => {
        if (buffer.length < sig.length) return false;
        
        for (let i = 0; i < sig.length; i++) {
          if (buffer[i] !== sig[i]) return false;
        }
        
        // Special case for WebP - check for WEBP signature at offset 8
        if (declaredMimeType === "image/webp") {
          const webpSig = [0x57, 0x45, 0x42, 0x50]; // "WEBP"
          if (buffer.length < 12) return false;
          for (let i = 0; i < webpSig.length; i++) {
            if (buffer[8 + i] !== webpSig[i]) return false;
          }
        }
        
        return true;
      });

    } catch (error) {
      functions.logger.error("MIME type validation error:", error);
      return false;
    }
  }

  /**
   * Sanitizes image by removing metadata and potential threats
   */
  static async sanitizeImage(buffer: Buffer): Promise<Buffer> {
    try {
      return await sharp(buffer)
        .withMetadata({}) // Remove EXIF and other metadata
        .toBuffer();
    } catch (error) {
      functions.logger.error("Image sanitization error:", error);
      throw new functions.https.HttpsError(
        "internal",
        "Failed to sanitize image"
      );
    }
  }
}
