import * as functions from "firebase-functions";
import sharp from "sharp";
import {CONFIG} from "../config";
import {ImageValidator} from "./imageValidator";

export interface ProcessingOptions {
  bucket: "farm-images" | "profile-images";
  maxDimension?: number;
  quality?: number;
  format?: "jpeg" | "png" | "webp";
  removeMetadata?: boolean;
  generateThumbnail?: boolean;
  thumbnailSize?: number;
}

export interface ProcessedImage {
  main: Buffer;
  thumbnail?: Buffer;
  metadata: {
    originalSize: number;
    processedSize: number;
    originalDimensions: { width: number; height: number };
    processedDimensions: { width: number; height: number };
    format: string;
    compressionRatio: number;
  };
}

/**
 * Advanced image processing utility
 */
export class ImageProcessor {
  
  /**
   * Process image with comprehensive validation and optimization
   */
  static async processImage(
    buffer: Buffer,
    options: ProcessingOptions
  ): Promise<ProcessedImage> {
    try {
      // Validate image first
      const validation = await ImageValidator.validateImage(
        buffer,
        CONFIG.MAX_FILE_SIZE[options.bucket]
      );

      if (!validation.isValid) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          validation.error || "Image validation failed"
        );
      }

      const originalMetadata = validation.metadata!;
      
      // Sanitize image (remove metadata for security)
      let processedBuffer = buffer;
      if (options.removeMetadata !== false) {
        processedBuffer = await ImageValidator.sanitizeImage(buffer);
      }

      // Determine processing parameters
      const maxDimension = options.maxDimension || CONFIG.MAX_DIMENSIONS[options.bucket];
      const quality = options.quality || CONFIG.IMAGE_QUALITY;
      const targetFormat = options.format || ImageProcessor.determineOptimalFormat(originalMetadata);

      // Process main image
      const mainImage = await ImageProcessor.processMainImage(
        processedBuffer,
        maxDimension,
        quality,
        targetFormat
      );

      // Generate thumbnail if requested
      let thumbnail: Buffer | undefined;
      if (options.generateThumbnail) {
        const thumbnailSize = options.thumbnailSize || 150;
        thumbnail = await ImageProcessor.generateThumbnail(
          processedBuffer,
          thumbnailSize,
          quality,
          targetFormat
        );
      }

      // Get processed image metadata
      const processedMetadata = await sharp(mainImage.buffer).metadata();

      return {
        main: mainImage.buffer,
        thumbnail,
        metadata: {
          originalSize: buffer.length,
          processedSize: mainImage.buffer.length,
          originalDimensions: {
            width: originalMetadata.width,
            height: originalMetadata.height
          },
          processedDimensions: {
            width: processedMetadata.width || 0,
            height: processedMetadata.height || 0
          },
          format: targetFormat,
          compressionRatio: Math.round((1 - mainImage.buffer.length / buffer.length) * 100)
        }
      };

    } catch (error) {
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      functions.logger.error("Image processing error:", error);
      throw new functions.https.HttpsError(
        "internal",
        "Failed to process image"
      );
    }
  }

  /**
   * Process main image with resizing and optimization
   */
  private static async processMainImage(
    buffer: Buffer,
    maxDimension: number,
    quality: number,
    format: "jpeg" | "png" | "webp"
  ): Promise<{ buffer: Buffer }> {
    let processor = sharp(buffer);

    // Get original dimensions
    const metadata = await processor.metadata();
    
    // Resize if necessary
    if (metadata.width && metadata.height) {
      const maxCurrentDimension = Math.max(metadata.width, metadata.height);
      
      if (maxCurrentDimension > maxDimension) {
        processor = processor.resize(maxDimension, maxDimension, {
          fit: "inside",
          withoutEnlargement: true
        });
      }
    }

    // Apply format-specific optimization
    switch (format) {
      case "jpeg":
        processor = processor.jpeg({
          quality,
          progressive: true,
          mozjpeg: true // Use mozjpeg encoder for better compression
        });
        break;
      
      case "png":
        processor = processor.png({
          quality,
          compressionLevel: 9,
          progressive: true
        });
        break;
      
      case "webp":
        processor = processor.webp({
          quality,
          effort: 6 // Higher effort for better compression
        });
        break;
    }

    const processedBuffer = await processor.toBuffer();
    
    return { buffer: processedBuffer };
  }

  /**
   * Generate thumbnail image
   */
  private static async generateThumbnail(
    buffer: Buffer,
    size: number,
    quality: number,
    format: "jpeg" | "png" | "webp"
  ): Promise<Buffer> {
    let processor = sharp(buffer)
      .resize(size, size, {
        fit: "cover",
        position: "center"
      });

    // Apply format-specific settings for thumbnail
    switch (format) {
      case "jpeg":
        processor = processor.jpeg({ quality: Math.max(quality - 10, 60) });
        break;
      case "png":
        processor = processor.png({ quality: Math.max(quality - 10, 60) });
        break;
      case "webp":
        processor = processor.webp({ quality: Math.max(quality - 10, 60) });
        break;
    }

    return await processor.toBuffer();
  }

  /**
   * Determine optimal output format based on input
   */
  private static determineOptimalFormat(
    metadata: { format: string; hasAlpha: boolean }
  ): "jpeg" | "png" | "webp" {
    // Keep PNG for images with transparency
    if (metadata.hasAlpha && metadata.format === "png") {
      return "png";
    }

    // Use WebP for modern browsers (when supported)
    // For now, default to JPEG for maximum compatibility
    return "jpeg";
  }

  /**
   * Estimate processing time based on image size and operations
   */
  static estimateProcessingTime(
    fileSize: number,
    operations: string[]
  ): number {
    // Base time: 100ms per MB
    let estimatedTime = (fileSize / (1024 * 1024)) * 100;

    // Add time for specific operations
    operations.forEach(op => {
      switch (op) {
        case "resize":
          estimatedTime += 50;
          break;
        case "thumbnail":
          estimatedTime += 30;
          break;
        case "format_conversion":
          estimatedTime += 40;
          break;
        case "metadata_removal":
          estimatedTime += 10;
          break;
      }
    });

    // Minimum 200ms, maximum 30 seconds
    return Math.max(200, Math.min(estimatedTime, 30000));
  }

  /**
   * Validate processing options
   */
  static validateProcessingOptions(options: ProcessingOptions): void {
    if (options.maxDimension && (options.maxDimension < 32 || options.maxDimension > 8192)) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Max dimension must be between 32 and 8192 pixels"
      );
    }

    if (options.quality && (options.quality < 1 || options.quality > 100)) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Quality must be between 1 and 100"
      );
    }

    if (options.thumbnailSize && (options.thumbnailSize < 16 || options.thumbnailSize > 512)) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Thumbnail size must be between 16 and 512 pixels"
      );
    }
  }
}
