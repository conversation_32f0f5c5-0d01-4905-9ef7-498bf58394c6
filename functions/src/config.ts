// Configuration constants
export const CONFIG = {
  // File size limits (in bytes)
  MAX_FILE_SIZE: {
    "farm-images": parseInt(process.env.MAX_FARM_IMAGE_SIZE_MB || "50") * 1024 * 1024,
    "profile-images": parseInt(process.env.MAX_PROFILE_IMAGE_SIZE_MB || "10") * 1024 * 1024,
  },

  // Image processing settings
  IMAGE_QUALITY: parseInt(process.env.IMAGE_QUALITY || "85"),
  MAX_DIMENSIONS: {
    "farm-images": parseInt(process.env.MAX_IMAGE_DIMENSION_FARM || "2048"),
    "profile-images": parseInt(process.env.MAX_IMAGE_DIMENSION_PROFILE || "512"),
  },
  
  // Allowed MIME types
  ALLOWED_MIME_TYPES: [
    "image/jpeg",
    "image/jpg", 
    "image/png",
    "image/webp",
    "image/gif"
  ],
  
  // CORS settings
  CORS: {
    ALLOWED_ORIGINS: [
      "http://localhost:3000",
      "https://localhost:3000",
      "https://bvr-safaris.vercel.app",
      "https://bvr-safaris-git-feature-firbase-7d2165-pienaar-ankers-projects.vercel.app"
    ],
ALLOWED_METHODS: ["POST", "OPTIONS", "GET", "PUT", "DELETE"],
    ALLOWED_HEADERS: ["Content-Type", "Authorization", "Accept", "Origin", "X-Requested-With"],
  }
};

// Helper function to get environment-specific config
export function getEnvironmentConfig() {
  const isDevelopment = process.env.NODE_ENV === "development";

  return {
    isDevelopment,
    projectId: process.env.GCLOUD_PROJECT || process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  };
}
