{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "functions": {"source": "functions", "runtime": "nodejs20", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "storage": {"port": 9199}, "functions": {"port": 5001}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}