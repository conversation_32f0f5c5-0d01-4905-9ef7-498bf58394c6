{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "migrate:firebase": "cd ../../scripts && npx tsx migrate-to-firebase.ts", "functions:build": "cd ../../functions && npm run build", "functions:serve": "cd ../../functions && npm run serve", "functions:deploy": "cd ../../functions && npm run deploy"}, "dependencies": {"@googlemaps/google-maps-services-js": "^3.4.1", "@types/node-fetch": "^2.6.12", "clsx": "^2.1.1", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "lucide-react": "^0.511.0", "next": "15.3.2", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "tsx": "^4.19.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5"}}