'use client'

import { useState, useEffect, createContext, useContext, ReactNode } from 'react'
import {
  User,
  onAuthStateChanged,
  signOut as firebaseSignOut,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  updateProfile,
  sendPasswordResetEmail
} from 'firebase/auth'
import { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase/client'
import { UserProfile, UserRole } from '@/lib/types/firestore'

interface FirebaseError extends Error {
  code: string
}

interface AuthContextType {
  user: User | null
  userProfile: UserProfile | null
  loading: boolean
  error: string | null
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData: SignUpData) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  clearError: () => void
  refreshProfile: () => Promise<void>
}

interface SignUpData {
  firstName: string
  lastName: string
  phone?: string
  role: UserRole
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch user profile from Firestore
  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      const docRef = doc(db, 'users', userId)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        const data = docSnap.data()
        return {
          id: docSnap.id,
          ...data,
          createdAt: data.createdAt?.toDate?.() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.() || data.updatedAt,
        } as UserProfile
      }
      return null
    } catch (err) {
      console.error('Error fetching user profile:', err)
      return null
    }
  }

  // Refresh user profile
  const refreshProfile = async () => {
    if (user) {
      const profile = await fetchUserProfile(user.uid)
      setUserProfile(profile)
    }
  }

  // Helper function to wait for auth state to be updated
  const waitForAuthState = (expectedUser: User | null): Promise<void> => {
    return new Promise((resolve) => {
      const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
        if (expectedUser && currentUser && currentUser.uid === expectedUser.uid) {
          unsubscribe()
          resolve()
        } else if (!expectedUser && !currentUser) {
          unsubscribe()
          resolve()
        }
      })
    })
  }

  // Listen to auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user)

      if (user) {
        // Get the ID token and set it as a cookie for middleware
        try {
          const idToken = await user.getIdToken()
          // Set the token as a cookie that expires in 1 hour
          document.cookie = `firebase-token=${idToken}; path=/; max-age=3600; secure; samesite=strict`
        } catch (error) {
          console.error('Error getting ID token:', error)
        }

        // Fetch user profile when user is authenticated
        const profile = await fetchUserProfile(user.uid)
        setUserProfile(profile)
      } else {
        // Clear the cookie when user signs out
        document.cookie = 'firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
        setUserProfile(null)
      }

      setLoading(false)
    })

    return unsubscribe
  }, [])

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      setError(null)
      setLoading(true)
      const userCredential = await signInWithEmailAndPassword(auth, email, password)

      // Wait for the auth state to be properly updated
      await waitForAuthState(userCredential.user)

      // Small additional delay to ensure cookie is set
      await new Promise(resolve => setTimeout(resolve, 100))

    } catch (err: unknown) {
      const errorCode = err instanceof Error && 'code' in err ? (err as FirebaseError).code : 'unknown'
      setError(getAuthErrorMessage(errorCode))
      throw err
    } finally {
      setLoading(false)
    }
  }

  // Sign up function
  const signUp = async (email: string, password: string, userData: SignUpData) => {
    try {
      setError(null)
      setLoading(true)

      // Create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)
      const user = userCredential.user

      // Update user profile with display name
      await updateProfile(user, {
        displayName: `${userData.firstName} ${userData.lastName}`
      })

      // Create user profile document in Firestore
      const now = Timestamp.now()
      const profileData = {
        email: email,
        fullName: `${userData.firstName} ${userData.lastName}`,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone || null,
        role: userData.role,
        languagePreference: 'en',
        createdAt: now,
        updatedAt: now
      }

      await setDoc(doc(db, 'users', user.uid), profileData)

      // Set the profile in state
      setUserProfile({
        id: user.uid,
        ...profileData,
        phone: userData.phone || undefined
      })

      // Set custom claims for role-based access control
      try {
        const idToken = await user.getIdToken()
        const response = await fetch('/api/auth/set-role', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${idToken}`,
          },
          body: JSON.stringify({
            uid: user.uid,
            role: userData.role
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          console.error('Failed to set user role:', errorData.error)
          // Don't throw here - user is created, just log the error
          // The user can still use the app, but role-based permissions might not work correctly
        } else {
          console.log('User role set successfully')
        }
      } catch (roleError) {
        console.error('Error setting user role:', roleError)
        // Don't throw here - user is created, just log the error
      }

      // Wait for the auth state to be properly updated
      await waitForAuthState(user)

      // Small additional delay to ensure cookie is set
      await new Promise(resolve => setTimeout(resolve, 100))

    } catch (err: unknown) {
      const errorCode = err instanceof Error && 'code' in err ? (err as FirebaseError).code : 'unknown'
      setError(getAuthErrorMessage(errorCode))
      throw err
    } finally {
      setLoading(false)
    }
  }

  // Sign out function
  const signOut = async () => {
    try {
      setError(null)
      await firebaseSignOut(auth)
      setUserProfile(null)
    } catch (err: unknown) {
      const errorCode = err instanceof Error && 'code' in err ? (err as FirebaseError).code : 'unknown'
      setError(getAuthErrorMessage(errorCode))
      throw err
    }
  }

  // Reset password function
  const resetPassword = async (email: string) => {
    try {
      setError(null)
      await sendPasswordResetEmail(auth, email)
    } catch (err: unknown) {
      const errorCode = err instanceof Error && 'code' in err ? (err as FirebaseError).code : 'unknown'
      setError(getAuthErrorMessage(errorCode))
      throw err
    }
  }

  // Clear error function
  const clearError = () => {
    setError(null)
  }

  const value: AuthContextType = {
    user,
    userProfile,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
    clearError,
    refreshProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Helper function to get user-friendly error messages
function getAuthErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case 'auth/user-not-found':
    case 'auth/wrong-password':
    case 'auth/invalid-credential':
      return 'Invalid email or password'
    case 'auth/email-already-in-use':
      return 'An account with this email already exists'
    case 'auth/weak-password':
      return 'Password should be at least 6 characters'
    case 'auth/invalid-email':
      return 'Invalid email address'
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later'
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection'
    default:
      return 'An unexpected error occurred. Please try again'
  }
}

// Helper hook for checking if user is authenticated
export function useRequireAuth() {
  const { user, loading } = useAuth()
  return { user, loading, isAuthenticated: !!user }
}

// Helper hook for checking user role
export function useUserRole(): UserRole | null {
  const { userProfile } = useAuth()
  return userProfile?.role || null
}