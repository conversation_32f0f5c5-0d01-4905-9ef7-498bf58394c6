/**
 * Hero image service for fetching random background images from local banner_images directory
 */

// Available banner images in the public/banner_images/ directory
const BANNER_IMAGES = [
  'IMG_6015-min.JPG',
  'IMG_6077-min.JPG',
  'IMG_6207-min.JPG',
  'IMG_6297-min.JPG',
  'IMG_6333-min.JPG',
  'IMG_6395-min.JPG',
  'IMG_6498-min.JPG',
  'IMG_6610-min.JPG',
  'IMG_6632-min.JPG',
  'IMG_6695-min.JPG',
  'IMG_6738-min.JPG',
  'IMG_6744-min.JPG',
  'IMG_6784-min.JPG'
]

// Default banner image path (using first banner image as default)
export const DEFAULT_HERO_IMAGE = `/banner_images/${BANNER_IMAGES[0]}`

export interface HeroImageMetadata {
  name: string
  fullPath: string
  downloadURL: string
}

/**
 * Fetches a random hero image URL from local banner_images directory
 * @returns Promise<string> - Path to a random hero image, or default banner if none available
 */
export async function getRandomHeroImageUrl(): Promise<string> {
  try {
    // Check if there are any images available
    if (!BANNER_IMAGES || BANNER_IMAGES.length === 0) {
      console.warn('No hero images found in banner_images directory, using default banner')
      return DEFAULT_HERO_IMAGE
    }

    // Pick a random image from the available items
    const randomIndex = Math.floor(Math.random() * BANNER_IMAGES.length)
    const randomImageName = BANNER_IMAGES[randomIndex]

    // Return the path to the selected image
    const imagePath = `/banner_images/${randomImageName}`

    return imagePath
  } catch (error) {
    // Fallback to default banner on error
    console.error('Error selecting random hero image from local directory, using default banner:', error)
    return DEFAULT_HERO_IMAGE
  }
}

/**
 * Gets the default hero image URL (immediate, no async needed)
 * @returns string - Path to the default banner image
 */
export function getDefaultHeroImageUrl(): string {
  return DEFAULT_HERO_IMAGE
}

/**
 * Fetches all available hero images with metadata
 * @returns Promise<HeroImageMetadata[]> - Array of hero image metadata
 */
export async function getAllHeroImages(): Promise<HeroImageMetadata[]> {
  try {
    if (!BANNER_IMAGES || BANNER_IMAGES.length === 0) {
      return []
    }

    // Create metadata for all available images
    const heroImages: HeroImageMetadata[] = BANNER_IMAGES.map((imageName) => {
      const imagePath = `/banner_images/${imageName}`
      return {
        name: imageName,
        fullPath: imagePath,
        downloadURL: imagePath
      }
    })

    return heroImages
  } catch (error) {
    console.error('Error fetching all hero images from local directory:', error)
    return []
  }
}

/**
 * Preloads a random hero image for better performance
 * @returns Promise<string | null> - Path of the preloaded image
 */
export async function preloadRandomHeroImage(): Promise<string | null> {
  const imageUrl = await getRandomHeroImageUrl()

  if (imageUrl) {
    // Create an image element to preload the image
    const img = new Image()
    img.src = imageUrl

    // Return the URL after initiating preload
    return imageUrl
  }

  return null
}
