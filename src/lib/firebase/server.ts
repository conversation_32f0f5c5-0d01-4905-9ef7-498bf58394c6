import { initializeApp, getApps, cert } from 'firebase-admin/app'
import { getAuth } from 'firebase-admin/auth'
import { getFirestore } from 'firebase-admin/firestore'
import { getStorage } from 'firebase-admin/storage'

// Initialize Firebase Admin SDK for server-side operations (API routes only)
// WARNING: This cannot be used in middleware due to Edge Runtime limitations
const initializeFirebaseAdmin = () => {
  if (getApps().length === 0) {
    const serviceAccount = process.env.FIREBASE_SERVICE_ACCOUNT_KEY

    if (!serviceAccount) {
      throw new Error('FIREBASE_SERVICE_ACCOUNT_KEY environment variable is required for server-side Firebase operations')
    }

    return initializeApp({
      credential: cert(JSON.parse(serviceAccount)),
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    })
  }

  return getApps()[0]
}

// Lazy initialization to avoid issues with Edge Runtime
let adminApp: ReturnType<typeof initializeFirebaseAdmin> | null = null
let adminAuth: ReturnType<typeof getAuth> | null = null
let adminDb: ReturnType<typeof getFirestore> | null = null
let adminStorage: ReturnType<typeof getStorage> | null = null

// Helper function to get admin services (for API routes only)
export const getAdminServices = () => {
  if (!adminApp) {
    adminApp = initializeFirebaseAdmin()
    adminAuth = getAuth(adminApp)
    adminDb = getFirestore(adminApp)
    adminStorage = getStorage(adminApp)
  }

  return {
    adminAuth,
    adminDb,
    adminStorage
  }
}

// Helper function to verify ID token on server
export const verifyIdToken = async (idToken: string) => {
  try {
    const { adminAuth } = getAdminServices()
    if (!adminAuth) {
      throw new Error('Firebase Admin Auth not initialized')
    }
    const decodedToken = await adminAuth.verifyIdToken(idToken)
    return decodedToken
  } catch (error) {
    console.error('Error verifying ID token:', error)
    throw error
  }
}

// Helper function to get user by UID
export const getUserByUid = async (uid: string) => {
  try {
    const { adminAuth } = getAdminServices()
    if (!adminAuth) {
      throw new Error('Firebase Admin Auth not initialized')
    }
    const userRecord = await adminAuth.getUser(uid)
    return userRecord
  } catch (error) {
    console.error('Error getting user:', error)
    throw error
  }
}

// Helper function to set custom claims
export const setCustomClaims = async (uid: string, claims: Record<string, unknown>) => {
  try {
    const { adminAuth } = getAdminServices()
    if (!adminAuth) {
      throw new Error('Firebase Admin Auth not initialized')
    }
    await adminAuth.setCustomUserClaims(uid, claims)
  } catch (error) {
    console.error('Error setting custom claims:', error)
    throw error
  }
}
