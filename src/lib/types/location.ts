/**
 * Location-related types for BVR Safaris geolocation functionality
 */

import { GeoPoint } from 'firebase/firestore'

// Google Places API response types
export interface LocationSearchResult {
  placeId: string
  description: string
  mainText: string
  secondaryText: string
  types: string[]
  structuredFormatting?: {
    mainText: string
    secondaryText?: string
  }
}

export interface AddressComponent {
  longName: string
  shortName: string
  types: string[]
}

export interface PlaceDetails {
  placeId: string
  formattedAddress: string
  coordinates: {
    latitude: number
    longitude: number
  }
  addressComponents: any // Simplified structure from our API
  types: string[]
  viewport?: {
    northeast: { lat: number; lng: number }
    southwest: { lat: number; lng: number }
  }
  name?: string
}

// Enhanced location data structure for farms
export interface LocationData {
  placeId: string
  formattedAddress: string
  coordinates: GeoPoint // Firestore GeoPoint for geo queries
  addressComponents: {
    streetNumber?: string
    route?: string
    locality?: string // City/Town
    sublocality?: string // Suburb/Area
    administrativeAreaLevel1: string // Province
    administrativeAreaLevel2?: string // District/Municipality
    postalCode?: string
    country: string // Should be "South Africa"
  }
  placeTypes: string[]
  viewport?: {
    northeast: GeoPoint
    southwest: GeoPoint
  }
  name?: string // Business name if applicable
}

// Search parameters for location-based queries
export interface LocationSearchParams {
  center?: { lat: number; lng: number }
  radius?: number // in kilometers
  bounds?: {
    northeast: { lat: number; lng: number }
    southwest: { lat: number; lng: number }
  }
  formattedAddress?: string
  text?: string // fallback text search
}

// Distance calculation result
export interface DistanceResult {
  distance: number // in kilometers
  duration?: number // in minutes (if available)
}

// Geocoding result
export interface GeocodeResult {
  coordinates: { lat: number; lng: number }
  formattedAddress: string
  addressComponents: AddressComponent[]
  placeId?: string
  types: string[]
}

// Location service configuration
export interface LocationServiceConfig {
  apiKey: string
  countryRestriction?: string // Default: 'za' for South Africa
  language?: string // Default: 'en'
  region?: string // Default: 'za'
}

// Cache entry for location data
export interface LocationCacheEntry {
  data: any
  timestamp: number
  ttl: number // time to live in milliseconds
}

// Error types for location services
export enum LocationErrorType {
  API_ERROR = 'API_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface LocationError {
  type: LocationErrorType
  message: string
  originalError?: any
}

// Utility types for component props
export interface LocationAutocompleteProps {
  value?: string
  onLocationSelect: (location: PlaceDetails) => void
  onInputChange?: (input: string) => void
  placeholder?: string
  countryRestriction?: string
  types?: string[]
  className?: string
  disabled?: boolean
  required?: boolean
  error?: string
}

export interface LocationDisplayProps {
  locationData: LocationData
  showFullAddress?: boolean
  showDistance?: boolean
  userLocation?: { lat: number; lng: number }
  className?: string
}

export interface DistanceFilterProps {
  value?: number
  onChange: (radius: number) => void
  options?: number[] // Available radius options in km
  className?: string
}

// Search filter types with location support
export interface EnhancedSearchFilters {
  query?: string
  location?: LocationSearchParams
  provinces?: string[]
  activities?: string[]
  priceRange?: [number, number]
  sortBy?: 'distance' | 'rating' | 'created' | 'name' | 'price'
  limit?: number
}

// Farm with distance information
export interface FarmWithDistance {
  id: string
  name: string
  locationData?: LocationData
  distance?: number
  [key: string]: any // Other farm properties
}

// Browser geolocation types
export interface BrowserLocationOptions {
  enableHighAccuracy?: boolean
  timeout?: number
  maximumAge?: number
}

export interface BrowserLocationResult {
  coordinates: { lat: number; lng: number }
  accuracy: number
  timestamp: number
}

// Rate limiting configuration
export interface RateLimitConfig {
  requestsPerSecond: number
  burstLimit: number
  windowMs: number
}

// Service response wrapper
export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: LocationError
  cached?: boolean
}

// Province mapping for South African addresses
export const PROVINCE_MAPPING: Record<string, string> = {
  'Eastern Cape': 'Eastern Cape',
  'Free State': 'Free State',
  'Gauteng': 'Gauteng',
  'KwaZulu-Natal': 'KwaZulu-Natal',
  'Limpopo': 'Limpopo',
  'Mpumalanga': 'Mpumalanga',
  'Northern Cape': 'Northern Cape',
  'North West': 'North West',
  'Western Cape': 'Western Cape'
}

// Common place types for South African locations
export const SOUTH_AFRICAN_PLACE_TYPES = {
  ESTABLISHMENT: 'establishment',
  GEOCODE: 'geocode',
  LOCALITY: 'locality',
  SUBLOCALITY: 'sublocality',
  ADMINISTRATIVE_AREA_LEVEL_1: 'administrative_area_level_1',
  ADMINISTRATIVE_AREA_LEVEL_2: 'administrative_area_level_2',
  COUNTRY: 'country',
  POSTAL_CODE: 'postal_code'
} as const

// Default configuration values
export const DEFAULT_LOCATION_CONFIG = {
  COUNTRY_RESTRICTION: 'za',
  LANGUAGE: 'en',
  REGION: 'za',
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  DEBOUNCE_DELAY: 300, // milliseconds
  DEFAULT_RADIUS_OPTIONS: [5, 10, 25, 50, 100], // kilometers
  MAX_AUTOCOMPLETE_RESULTS: 5,
  GEOLOCATION_TIMEOUT: 10000, // 10 seconds
  GEOLOCATION_MAX_AGE: 60000 // 1 minute
} as const
