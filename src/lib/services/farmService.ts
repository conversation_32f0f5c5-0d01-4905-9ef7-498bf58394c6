/**
 * Enhanced farm service with location-based functionality
 * Extends the basic Firestore operations with geolocation features
 */

import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  doc,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
  GeoPoint,
  Query,
  DocumentData,
  startAfter,
  QueryDocumentSnapshot
} from 'firebase/firestore'
import { db } from '@/lib/firebase/client'
import {
  GameFarm,
  FarmSearchFilters,
  FarmWithDistance,
  FarmSearchResult,
  CreateFarmData
} from '@/lib/types/firestore'
import { LocationData } from '@/lib/types/location'
import { DistanceService } from '@/lib/services/location/distance'
import { LocationService, locationUtils } from '@/lib/services/location'
import { docToData } from '@/lib/firebase/firestore'

export class EnhancedFarmService {
  private locationService: LocationService

  constructor() {
    this.locationService = LocationService.getInstance()
  }

  /**
   * Search farms with location-based filtering and sorting
   */
  async searchFarms(filters: FarmSearchFilters = {}): Promise<FarmSearchResult> {
    let baseQuery: Query<DocumentData> = collection(db, 'farms')
    
    // Apply basic filters
    baseQuery = this.applyBasicFilters(baseQuery, filters)
    
    // Get initial results
    let querySnapshot = await getDocs(baseQuery)
    let farms = querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)

    // Apply location-based filtering and sorting
    if (filters.location) {
      farms = await this.applyLocationFilters(farms, filters.location, filters.sortBy)
    }

    // Apply other client-side filters
    farms = this.applyClientSideFilters(farms, filters)

    // Apply pagination
    const { farms: paginatedFarms, hasMore } = this.applyPagination(farms, filters)

    return {
      farms: paginatedFarms,
      total: farms.length,
      hasMore,
      searchCenter: filters.location?.center,
      searchRadius: filters.location?.radius
    }
  }

  /**
   * Get farms near a specific location
   */
  async getFarmsNearLocation(
    center: { lat: number; lng: number },
    radiusKm: number = 50,
    limitCount: number = 20
  ): Promise<FarmWithDistance[]> {
    // Get all active farms first
    const q = query(
      collection(db, 'farms'),
      where('isActive', '==', true),
      limit(limitCount * 2) // Get more than needed to account for distance filtering
    )

    const querySnapshot = await getDocs(q)
    const farms = querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)

    // Filter by distance and add distance information
    const farmsWithDistance: FarmWithDistance[] = []

    for (const farm of farms) {
      if (farm.locationData?.coordinates) {
        const farmCoords = {
          lat: farm.locationData.coordinates.latitude,
          lng: farm.locationData.coordinates.longitude
        }

        const distance = DistanceService.calculateDistance(center, farmCoords)
        
        if (distance <= radiusKm) {
          const travelTime = DistanceService.estimateTravelTime(distance)
          farmsWithDistance.push({
            ...farm,
            distance,
            travelTime: travelTime.duration
          })
        }
      }
    }

    // Sort by distance and limit results
    return farmsWithDistance
      .sort((a, b) => (a.distance || 0) - (b.distance || 0))
      .slice(0, limitCount)
  }

  /**
   * Create a new farm with enhanced location data
   */
  async createFarm(data: CreateFarmData, ownerId?: string): Promise<string> {
    const now = new Date()

    // Generate additional location-based fields
    const enhancedData: Partial<GameFarm> = {
      ...data,
      ownerId: data.ownerId || ownerId, // Use provided ownerId or fallback
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now),
      isActive: true,
      featured: false
    }

    // Generate searchable location and geohash if location data exists
    if (data.locationData) {
      enhancedData.searchableLocation = LocationService.createSearchableLocation(data.locationData)
      enhancedData.geoHash = LocationService.generateGeoHash({
        lat: data.locationData.coordinates.latitude,
        lng: data.locationData.coordinates.longitude
      })
      
      // Ensure province is set from location data
      const mappedProvince = LocationService.mapToSouthAfricanProvince(data.locationData)
      if (mappedProvince) {
        enhancedData.province = mappedProvince
      }
    }

    const docRef = await addDoc(collection(db, 'farms'), enhancedData)
    return docRef.id
  }

  /**
   * Update farm with location data processing
   */
  async updateFarm(farmId: string, data: Partial<GameFarm>): Promise<void> {
    const updateData: Partial<GameFarm> = {
      ...data,
      updatedAt: Timestamp.now()
    }

    // Update location-based fields if location data changed
    if (data.locationData) {
      updateData.searchableLocation = LocationService.createSearchableLocation(data.locationData)
      updateData.geoHash = LocationService.generateGeoHash({
        lat: data.locationData.coordinates.latitude,
        lng: data.locationData.coordinates.longitude
      })
      
      // Update province from location data
      const mappedProvince = LocationService.mapToSouthAfricanProvince(data.locationData)
      if (mappedProvince) {
        updateData.province = mappedProvince
      }
    }

    const docRef = doc(db, 'farms', farmId)
    await updateDoc(docRef, updateData)
  }

  /**
   * Get farm with distance from a reference point
   */
  async getFarmWithDistance(
    farmId: string,
    referencePoint?: { lat: number; lng: number }
  ): Promise<FarmWithDistance | null> {
    const docRef = doc(db, 'farms', farmId)
    const docSnap = await getDoc(docRef)
    const farm = docToData<GameFarm>(docSnap)

    if (!farm) return null

    const farmWithDistance: FarmWithDistance = { ...farm }

    if (referencePoint && farm.locationData?.coordinates) {
      const farmCoords = {
        lat: farm.locationData.coordinates.latitude,
        lng: farm.locationData.coordinates.longitude
      }

      farmWithDistance.distance = DistanceService.calculateDistance(referencePoint, farmCoords)
      const travelTime = DistanceService.estimateTravelTime(farmWithDistance.distance)
      farmWithDistance.travelTime = travelTime.duration
    }

    return farmWithDistance
  }

  /**
   * Apply basic Firestore filters
   */
  private applyBasicFilters(baseQuery: Query<DocumentData>, filters: FarmSearchFilters): Query<DocumentData> {
    let q = baseQuery

    // Always filter for active farms unless specifically requested
    q = query(q, where('isActive', '==', true))

    if (filters.provinces && filters.provinces.length > 0) {
      q = query(q, where('province', 'in', filters.provinces))
    }

    if (filters.activities && filters.activities.length > 0) {
      q = query(q, where('activityTypes', 'in', filters.activities))
    }

    // Text search using searchableLocation field
    if (filters.query) {
      // Note: Firestore doesn't support full-text search natively
      // This is a simplified approach - consider using Algolia or similar for production
      const searchTerms = filters.query.toLowerCase().split(' ')
      // We'll handle text search client-side for now
    }

    // Apply sorting (distance sorting will be handled client-side)
    if (filters.sortBy && filters.sortBy !== 'distance') {
      const sortField = this.getSortField(filters.sortBy)
      const sortOrder = filters.sortOrder || 'desc'
      q = query(q, orderBy(sortField, sortOrder))
    } else {
      // Default sorting
      q = query(q, orderBy('featured', 'desc'), orderBy('createdAt', 'desc'))
    }

    if (filters.limit) {
      q = query(q, limit(filters.limit))
    }

    return q
  }

  /**
   * Apply location-based filtering and sorting
   */
  private async applyLocationFilters(
    farms: GameFarm[],
    locationFilter: NonNullable<FarmSearchFilters['location']>,
    sortBy?: string
  ): Promise<FarmWithDistance[]> {
    const farmsWithDistance: FarmWithDistance[] = []

    for (const farm of farms) {
      if (!farm.locationData?.coordinates) continue

      const farmCoords = {
        lat: farm.locationData.coordinates.latitude,
        lng: farm.locationData.coordinates.longitude
      }

      let includeThisFarm = true
      let distance: number | undefined

      // Apply radius filter
      if (locationFilter.center && locationFilter.radius) {
        distance = DistanceService.calculateDistance(locationFilter.center, farmCoords)
        includeThisFarm = distance <= locationFilter.radius
      }

      // Apply bounds filter
      if (includeThisFarm && locationFilter.bounds) {
        const { northeast, southwest } = locationFilter.bounds
        includeThisFarm = (
          farmCoords.lat >= southwest.lat &&
          farmCoords.lat <= northeast.lat &&
          farmCoords.lng >= southwest.lng &&
          farmCoords.lng <= northeast.lng
        )
      }

      if (includeThisFarm) {
        const farmWithDistance: FarmWithDistance = { ...farm }
        
        if (distance !== undefined) {
          farmWithDistance.distance = distance
          const travelTime = DistanceService.estimateTravelTime(distance)
          farmWithDistance.travelTime = travelTime.duration
        }

        farmsWithDistance.push(farmWithDistance)
      }
    }

    // Sort by distance if requested
    if (sortBy === 'distance') {
      farmsWithDistance.sort((a, b) => (a.distance || 0) - (b.distance || 0))
    }

    return farmsWithDistance
  }

  /**
   * Apply client-side filters that can't be done in Firestore
   */
  private applyClientSideFilters(farms: FarmWithDistance[], filters: FarmSearchFilters): FarmWithDistance[] {
    let filteredFarms = [...farms]

    // Text search
    if (filters.query) {
      const searchTerms = filters.query.toLowerCase().split(' ')
      filteredFarms = filteredFarms.filter(farm => {
        const searchText = [
          farm.name,
          farm.description,
          farm.searchableLocation,
          locationUtils.getLocationDisplayName(farm.locationData!)
        ].join(' ').toLowerCase()

        return searchTerms.every(term => searchText.includes(term))
      })
    }

    // Price range filter
    if (filters.priceRange && filters.priceRange.length === 2) {
      const [minPrice, maxPrice] = filters.priceRange
      filteredFarms = filteredFarms.filter(farm => {
        if (!farm.pricePerDay) return true // Include farms without price info
        return farm.pricePerDay >= minPrice && farm.pricePerDay <= maxPrice
      })
    }

    // Size range filter
    if (filters.sizeRange && filters.sizeRange.length === 2) {
      const [minSize, maxSize] = filters.sizeRange
      filteredFarms = filteredFarms.filter(farm => {
        if (!farm.sizeHectares) return true // Include farms without size info
        return farm.sizeHectares >= minSize && farm.sizeHectares <= maxSize
      })
    }

    return filteredFarms
  }

  /**
   * Apply pagination
   */
  private applyPagination(
    farms: FarmWithDistance[],
    filters: FarmSearchFilters
  ): { farms: FarmWithDistance[]; hasMore: boolean } {
    const limit = filters.limit || 20
    const offset = filters.offset || 0

    const paginatedFarms = farms.slice(offset, offset + limit)
    const hasMore = farms.length > offset + limit

    return { farms: paginatedFarms, hasMore }
  }

  /**
   * Get Firestore field name for sorting
   */
  private getSortField(sortBy: string): string {
    switch (sortBy) {
      case 'name':
        return 'name'
      case 'created':
        return 'createdAt'
      case 'price':
        return 'pricePerDay'
      case 'size':
        return 'sizeHectares'
      case 'rating':
        return 'averageRating' // This would need to be calculated and stored
      default:
        return 'createdAt'
    }
  }
}

// Create and export singleton instance
export const enhancedFarmService = new EnhancedFarmService()
