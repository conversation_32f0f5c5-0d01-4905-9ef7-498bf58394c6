'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { useAuth } from '@/hooks/useAuth'
import { bookingService, userProfileService } from '@/lib/firebase/firestore'
import { GameFarm, ActivityType } from '@/lib/types/firestore'

interface BookingModalProps {
  farm: GameFarm
  activity: ActivityType
  onClose: () => void
}

interface BookingFormData {
  checkIn: string
  checkOut: string
  guests: number
  accommodation: string
  specialRequests: string
  contactPhone: string
}

export function BookingModal({ farm, activity, onClose }: BookingModalProps) {
  const { user } = useAuth()
  const router = useRouter()
  const [step, setStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState<BookingFormData>({
    checkIn: '',
    checkOut: '',
    guests: 2,
    accommodation: 'standard',
    specialRequests: '',
    contactPhone: ''
  })

  const handleInputChange = (field: keyof BookingFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const calculateDays = () => {
    if (!formData.checkIn || !formData.checkOut) return 0
    const checkIn = new Date(formData.checkIn)
    const checkOut = new Date(formData.checkOut)
    const diffTime = Math.abs(checkOut.getTime() - checkIn.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  const handleSubmit = async () => {
    if (!user) {
      router.push('/auth/login')
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const durationDays = calculateDays()

      // Create booking in Firebase
      await bookingService.create({
        farmId: farm.id,
        hunterId: user.uid,
        startDate: formData.checkIn,
        endDate: formData.checkOut,
        durationDays: durationDays,
        activityType: activity,
        groupSize: formData.guests,
        specialRequests: formData.specialRequests || undefined,
        status: 'pending'
      })

      // Update user profile with phone if provided
      if (formData.contactPhone) {
        await userProfileService.update(user.uid, {
          phone: formData.contactPhone
        })
      }

      // Success - close modal and redirect
      onClose()
      router.push('/dashboard')
    } catch (err) {
      console.error('Booking submission failed:', err)
      setError(err instanceof Error ? err.message : 'Failed to submit booking request')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!user) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Sign In Required</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-earth-700">
              Please sign in to submit a booking request.
            </p>
            <div className="flex gap-2">
              <Button variant="primary" className="flex-1" onClick={() => router.push('/auth/login')}>
                Sign In
              </Button>
              <Button variant="outline" className="flex-1" onClick={onClose}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Book Your Safari</CardTitle>
            <button 
              onClick={onClose}
              className="text-earth-500 hover:text-earth-700"
            >
              ✕
            </button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Farm & Activity Info */}
          <div className="bg-earth-50 p-4 rounded-lg">
            <h3 className="font-semibold text-earth-900 mb-2">{farm.name}</h3>
            <p className="text-earth-600 mb-3"> {farm.location}, {farm.province}</p>
            <Badge variant={activity === 'hunting' ? 'hunting' : 'photo'}>
              {activity === 'hunting' ? 'Hunting Safari' : activity === 'photo_safari' ? 'Photo Safari' : 'Combined Safari'}
            </Badge>
          </div>

          {step === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-earth-900">Step 1: Trip Details</h3>
              
              {/* Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-earth-700 mb-2">
                    Check-in Date
                  </label>
                  <Input
                    type="date"
                    value={formData.checkIn}
                    onChange={(e) => handleInputChange('checkIn', e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-earth-700 mb-2">
                    Check-out Date
                  </label>
                  <Input
                    type="date"
                    value={formData.checkOut}
                    onChange={(e) => handleInputChange('checkOut', e.target.value)}
                    min={formData.checkIn || new Date().toISOString().split('T')[0]}
                    required
                  />
                </div>
              </div>

              {/* Number of Guests */}
              <div>
                <label className="block text-sm font-medium text-earth-700 mb-2">
                  Number of Guests
                </label>
                <Input
                  type="number"
                  min="1"
                  max="12"
                  value={formData.guests}
                  onChange={(e) => handleInputChange('guests', parseInt(e.target.value))}
                  required
                />
              </div>

              {/* Accommodation */}
              <div>
                <label className="block text-sm font-medium text-earth-700 mb-2">
                  Accommodation Preference
                </label>
                <select
                  value={formData.accommodation}
                  onChange={(e) => handleInputChange('accommodation', e.target.value)}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                >
                  <option value="standard">Standard Room</option>
                  <option value="luxury">Luxury Suite</option>
                  <option value="self-catering">Self-Catering Chalet</option>
                </select>
              </div>

              {/* Trip Summary */}
              {formData.checkIn && formData.checkOut && (
                <div className="bg-accent-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-earth-900 mb-2">Trip Summary</h4>
                  <div className="space-y-1 text-sm text-earth-700">
                    <p>Duration: {calculateDays()} days</p>
                    <p>Guests: {formData.guests}</p>
                    <p>Activity: {activity === 'hunting' ? 'Hunting Safari' : activity === 'photo_safari' ? 'Photo Safari' : 'Combined Safari'}</p>
                    <p>Accommodation: {formData.accommodation.replace('-', ' ')}</p>
                  </div>
                </div>
              )}

              <div className="flex justify-end">
                <Button 
                  variant="primary" 
                  onClick={() => setStep(2)}
                  disabled={!formData.checkIn || !formData.checkOut || calculateDays() < 1}
                >
                  Continue
                </Button>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-earth-900">Step 2: Contact & Requests</h3>
              
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}
              
              {/* Contact Information */}
              <div>
                <label className="block text-sm font-medium text-earth-700 mb-2">
                  Contact Phone Number
                </label>
                <Input
                  type="tel"
                  placeholder="e.g., +27 12 345 6789"
                  value={formData.contactPhone}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  required
                />
              </div>

              {/* Special Requests */}
              <div>
                <label className="block text-sm font-medium text-earth-700 mb-2">
                  Special Requests or Requirements
                </label>
                <textarea
                  rows={4}
                  placeholder="Any dietary requirements, special occasions, equipment needs, or other requests..."
                  value={formData.specialRequests}
                  onChange={(e) => handleInputChange('specialRequests', e.target.value)}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent resize-none"
                />
              </div>

              {/* Important Notes */}
              <div className="bg-earth-50 p-4 rounded-lg">
                <h4 className="font-semibold text-earth-900 mb-2">Important Notes</h4>
                <ul className="space-y-1 text-sm text-earth-700">
                  <li>• This is a booking request, not a confirmed reservation</li>
                  <li>• The farm owner will review and respond within 24-48 hours</li>
                  <li>• Final pricing may vary based on specific requirements</li>
                  <li>• Cancellation policies apply as per farm terms</li>
                </ul>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setStep(1)} className="flex-1">
                  Back
                </Button>
                <Button 
                  variant="primary" 
                  onClick={handleSubmit}
                  disabled={isSubmitting || !formData.contactPhone}
                  className="flex-1"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Request'}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}