'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { LocationAutocomplete, DistanceFilter } from '@/components/ui/LocationAutocomplete'
import { LocationData } from '@/lib/types/location'

export interface SearchFilters {
  query: string
  locationData: LocationData | null
  radius?: number
}

export interface SearchBarProps {
  onSearch?: (filters: SearchFilters) => void
  placeholder?: string
  className?: string
  showDistanceFilter?: boolean
}

export function SearchBar({
  onSearch,
  placeholder = "Search farms, activities, or species...",
  className,
  showDistanceFilter = true
}: SearchBarProps) {
  const [query, setQuery] = useState('')
  const [locationData, setLocationData] = useState<LocationData | null>(null)
  const [radius, setRadius] = useState<number | undefined>(undefined)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch?.({
      query,
      locationData,
      radius
    })
  }

  const handleLocationSelect = (location: LocationData) => {
    setLocationData(location)
  }

  return (
    <form onSubmit={handleSubmit} className={className}>
      <div className="flex flex-col gap-4 p-6 bg-white rounded-lg shadow-lg">
        {/* Main search row */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <Input
              type="text"
              placeholder={placeholder}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="border-earth-300 focus:border-accent-600"
            />
          </div>

          <div className="flex-1 md:max-w-sm">
            <LocationAutocomplete
              value={locationData?.formattedAddress || ''}
              onLocationSelect={handleLocationSelect}
              placeholder="Search location..."
              className="border-earth-300 focus:border-accent-600"
            />
          </div>

          <Button
            type="submit"
            variant="primary"
            size="lg"
            className="md:px-8 whitespace-nowrap"
          >
            🔍 Search
          </Button>
        </div>

        {/* Distance filter row */}
        {showDistanceFilter && locationData && (
          <div className="flex flex-col md:flex-row gap-4 pt-2 border-t border-earth-200">
            <div className="md:max-w-xs">
              <DistanceFilter
                value={radius}
                onChange={setRadius}
                label="Search radius"
                className="w-full"
              />
            </div>

            <div className="flex-1 flex items-end">
              <p className="text-sm text-earth-600">
                {locationData.addressComponents.locality && (
                  <>Searching near <strong>{locationData.addressComponents.locality}</strong></>
                )}
                {radius && <> within <strong>{radius}km</strong></>}
              </p>
            </div>
          </div>
        )}
      </div>
    </form>
  )
}