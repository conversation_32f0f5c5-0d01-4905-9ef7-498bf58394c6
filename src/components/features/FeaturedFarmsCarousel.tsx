'use client'

import { useState, useEffect } from 'react'
import { FarmCard } from './FarmCard'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { GameFarm } from '@/lib/types/firestore'

interface FarmWithStats extends GameFarm {
  rating?: number
  reviewCount?: number
  activities: ('hunting' | 'photo_safari')[]
  imageUrl?: string
}

interface FeaturedFarmsCarouselProps {
  farms: FarmWithStats[]
  loading?: boolean
  error?: string | null
  onRetry?: () => void
}

export function FeaturedFarmsCarousel({ 
  farms, 
  loading = false, 
  error = null, 
  onRetry 
}: FeaturedFarmsCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || farms.length <= 1) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === farms.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(interval)
  }, [isAutoPlaying, farms.length])

  const goToPrevious = () => {
    setIsAutoPlaying(false)
    setCurrentIndex(currentIndex === 0 ? farms.length - 1 : currentIndex - 1)
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const goToNext = () => {
    setIsAutoPlaying(false)
    setCurrentIndex(currentIndex === farms.length - 1 ? 0 : currentIndex + 1)
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const goToSlide = (index: number) => {
    setIsAutoPlaying(false)
    setCurrentIndex(index)
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  if (loading) {
    return (
      <div className="text-center py-16">
        <p className="text-cream" style={{
          fontFamily: 'var(--font-body)',
          fontSize: 'var(--text-secondary-body)'
        }}>Loading featured farms...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-16">
        <p className="text-red-400 mb-4" style={{
          fontFamily: 'var(--font-body)',
          fontSize: 'var(--text-secondary-body)'
        }}>{error}</p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="mt-4 px-4 py-2 rounded-lg hover:opacity-80 transition-opacity"
            style={{
              backgroundColor: 'var(--deep-brown)',
              color: 'var(--cream)',
              fontFamily: 'var(--font-body)',
              fontSize: 'var(--text-secondary-body)',
              borderRadius: '12px'
            }}
          >
            Try Again
          </button>
        )}
      </div>
    )
  }

  if (farms.length === 0) {
    return null
  }

  return (
    <div className="relative">
      {/* Carousel Container */}
      <div className="relative overflow-hidden rounded-lg">
        <div 
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {farms.map((farm) => (
            <div key={farm.id} className="w-full flex-shrink-0 px-4">
              <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
                <FarmCard
                  id={farm.id}
                  name={farm.name}
                  location={farm.location}
                  province={farm.province}
                  description={farm.description || ''}
                  imageUrl={farm.imageUrl}
                  activities={farm.activities}
                  priceRange={farm.pricingInfo || 'Contact for pricing'}
                  rating={farm.rating}
                  reviewCount={farm.reviewCount}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Arrows */}
      {farms.length > 1 && (
        <>
          <button
            onClick={goToPrevious}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-10"
            aria-label="Previous farm"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
          
          <button
            onClick={goToNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-10"
            aria-label="Next farm"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        </>
      )}

      {/* Dots Indicator */}
      {farms.length > 1 && (
        <div className="flex justify-center mt-6 space-x-2">
          {farms.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentIndex 
                  ? 'bg-cream' 
                  : 'bg-cream/30 hover:bg-cream/50'
              }`}
              aria-label={`Go to farm ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  )
}
