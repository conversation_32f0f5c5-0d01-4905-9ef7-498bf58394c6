'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { X, Star, TrendingUp, Eye, Calendar } from 'lucide-react'
import { farmService } from '@/lib/firebase/firestore'

interface PromoteFarmModalProps {
  isOpen: boolean
  onClose: () => void
  farmId: string
  farmName: string
  currentlyFeatured: boolean
  onPromotionUpdate: (featured: boolean) => void
}

export function PromoteFarmModal({
  isOpen,
  onClose,
  farmId,
  farmName,
  currentlyFeatured,
  onPromotionUpdate
}: PromoteFarmModalProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  if (!isOpen) return null

  const handleToggleFeature = async () => {
    try {
      setLoading(true)
      setError(null)

      const newFeaturedStatus = !currentlyFeatured

      await farmService.update(farmId, {
        featured: newFeaturedStatus
      })

      onPromotionUpdate(newFeaturedStatus)
      onClose()
    } catch (err) {
      console.error('Error updating farm promotion:', err)
      setError('Failed to update farm promotion status')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Star className="w-5 h-5" />
              Promote Farm Listing
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="p-2"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Current Status */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-earth-900 mb-2">{farmName}</h3>
            <Badge variant={currentlyFeatured ? 'photo' : 'default'} className="mb-4">
              {currentlyFeatured ? 'Currently Featured' : 'Not Featured'}
            </Badge>
          </div>

          {/* Feature Benefits */}
          <div className="bg-earth-50 p-4 rounded-lg">
            <h4 className="font-medium text-earth-900 mb-3 flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Featured Listing Benefits
            </h4>
            <ul className="space-y-2 text-sm text-earth-600">
              <li className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-accent-600" />
                Appear at the top of search results
              </li>
              <li className="flex items-center gap-2">
                <Star className="w-4 h-4 text-accent-600" />
                Get highlighted with a &quot;Featured&quot; badge
              </li>
              <li className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-accent-600" />
                Increase visibility by up to 300%
              </li>
              <li className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-accent-600" />
                Get priority placement in recommendations
              </li>
            </ul>
          </div>

          {/* Promotion Options */}
          <div className="space-y-4">
            <h4 className="font-medium text-earth-900">Promotion Options</h4>

            {/* Free Featured Status */}
            <div className="border border-earth-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h5 className="font-medium text-earth-900">Featured Status</h5>
                <Badge variant="hunting">Free</Badge>
              </div>
              <p className="text-sm text-earth-600 mb-3">
                {currentlyFeatured
                  ? 'Remove your farm from featured listings'
                  : 'Add your farm to featured listings for better visibility'
                }
              </p>
              <Button
                variant={currentlyFeatured ? 'outline' : 'primary'}
                onClick={handleToggleFeature}
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </div>
                ) : currentlyFeatured ? (
                  'Remove from Featured'
                ) : (
                  'Make Featured'
                )}
              </Button>
            </div>

            {/* Premium Promotion (Coming Soon) */}
            <div className="border border-earth-200 rounded-lg p-4 opacity-60">
              <div className="flex items-center justify-between mb-2">
                <h5 className="font-medium text-earth-900">Premium Promotion</h5>
                <Badge variant="default">Coming Soon</Badge>
              </div>
              <p className="text-sm text-earth-600 mb-3">
                Boost your listing with premium placement, social media promotion, and email marketing
              </p>
              <Button variant="outline" disabled className="w-full">
                Premium Boost - R299/month
              </Button>
            </div>
          </div>

          {/* Tips */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-earth-900 mb-2">Promotion Tips</h4>
            <ul className="text-sm text-earth-600 space-y-1">
              <li>• Ensure your farm has high-quality photos</li>
              <li>• Complete all farm details and descriptions</li>
              <li>• Respond quickly to booking inquiries</li>
              <li>• Maintain good reviews and ratings</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
