'use client'

import { useState, useRef } from 'react'
import Image from 'next/image'
import { But<PERSON> } from './Button'
import { farmImageService, profileImageService } from '@/lib/firebase/storage'

interface FileUploadProps {
  onUpload: (url: string) => void
  bucket: 'farm-images' | 'profile-images'
  farmId?: string // Required for farm-images bucket
  userId?: string // Required for profile-images bucket
  accept?: string
  maxSize?: number // in MB
  className?: string
  children?: React.ReactNode
}

export function FileUpload({
  onUpload,
  bucket,
  farmId,
  userId,
  accept = 'image/*',
  maxSize = 5,
  className = '',
  children
}: FileUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [error, setError] = useState<string | null>(null)
  const [uploadMethod, setUploadMethod] = useState<'cloud-function' | 'direct' | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setError(null)
    setUploadProgress(0)
    setUploadMethod(null)

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      setError(`File size must be less than ${maxSize}MB`)
      return
    }

    // Validate file type
    if (accept && !file.type.match(accept.replace('*', '.*'))) {
      setError('Invalid file type')
      return
    }

    // Validate required parameters
    if (bucket === 'farm-images' && !farmId) {
      setError('Farm ID is required for farm image uploads')
      return
    }

    if (bucket === 'profile-images' && !userId) {
      setError('User ID is required for profile image uploads')
      return
    }

    setUploading(true)

    try {
      let downloadURL: string

      if (bucket === 'farm-images' && farmId) {
        console.log('Starting farm image upload for farmId:', farmId)

        // Track which upload method is being used
        const originalConsoleLog = console.log
        console.log = (...args) => {
          if (args[0]?.includes('Using Cloud Function')) {
            setUploadMethod('cloud-function')
          } else if (args[0]?.includes('falling back to direct upload')) {
            setUploadMethod('direct')
          }
          originalConsoleLog(...args)
        }

        downloadURL = await farmImageService.uploadFarmImage(farmId, file, {
          onProgress: (progress) => {
            console.log('Upload progress:', progress.progress)
            setUploadProgress(progress.progress)
          },
          onError: (error) => {
            console.error('Upload error:', error)
            setError(error.message)
          }
        })

        // Restore console.log
        console.log = originalConsoleLog
      } else if (bucket === 'profile-images' && userId) {
        console.log('Starting profile image upload for userId:', userId)

        // Track which upload method is being used
        const originalConsoleLog = console.log
        console.log = (...args) => {
          if (args[0]?.includes('Using Cloud Function')) {
            setUploadMethod('cloud-function')
          } else if (args[0]?.includes('falling back to direct upload')) {
            setUploadMethod('direct')
          }
          originalConsoleLog(...args)
        }

        downloadURL = await profileImageService.uploadProfileImage(userId, file, {
          onProgress: (progress) => {
            console.log('Upload progress:', progress.progress)
            setUploadProgress(progress.progress)
          },
          onError: (error) => {
            console.error('Upload error:', error)
            setError(error.message)
          }
        })

        // Restore console.log
        console.log = originalConsoleLog
      } else {
        throw new Error('Invalid bucket or missing required parameters')
      }

      onUpload(downloadURL)
      setUploadProgress(100)
    } catch (error) {
      console.error('Upload error:', error)
      setError(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setUploading(false)
      // Reset progress after a delay
      setTimeout(() => setUploadProgress(0), 2000)
    }
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={className}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileSelect}
        className="hidden"
      />

      {children ? (
        <div onClick={handleClick} className="cursor-pointer">
          {children}
        </div>
      ) : (
        <Button
          type="button"
          variant="outline"
          onClick={handleClick}
          isLoading={uploading}
          disabled={uploading}
        >
          {uploading ? `Uploading... ${Math.round(uploadProgress)}%` : 'Upload File'}
        </Button>
      )}

      {error && (
        <p className="text-red-600 text-sm mt-2">{error}</p>
      )}

      {uploading && uploadMethod && (
        <p className="text-blue-600 text-xs mt-1">
          {uploadMethod === 'cloud-function'
            ? '🚀 Using secure server-side upload'
            : '📁 Using direct upload'}
        </p>
      )}
    </div>
  )
}

interface ImageUploadProps extends Omit<FileUploadProps, 'accept'> {
  currentImage?: string
  alt?: string
}

export function ImageUpload({
  currentImage,
  alt = 'Upload preview',
  onUpload,
  bucket,
  farmId,
  userId,
  maxSize = 5,
  className = ''
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [error, setError] = useState<string | null>(null)
  const [preview, setPreview] = useState<string | null>(currentImage || null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setError(null)
    setUploadProgress(0)

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      setError(`File size must be less than ${maxSize}MB`)
      return
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file')
      return
    }

    // Validate required parameters
    if (bucket === 'farm-images' && !farmId) {
      setError('Farm ID is required for farm image uploads')
      return
    }

    if (bucket === 'profile-images' && !userId) {
      setError('User ID is required for profile image uploads')
      return
    }

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)

    setUploading(true)

    try {
      let downloadURL: string

      if (bucket === 'farm-images' && farmId) {
        console.log('Starting farm image upload for farmId:', farmId)
        downloadURL = await farmImageService.uploadFarmImage(farmId, file, {
          onProgress: (progress) => {
            console.log('Upload progress:', progress.progress)
            setUploadProgress(progress.progress)
          },
          onError: (error) => {
            console.error('Upload error:', error)
            setError(error.message)
          }
        })
      } else if (bucket === 'profile-images' && userId) {
        console.log('Starting profile image upload for userId:', userId)
        downloadURL = await profileImageService.uploadProfileImage(userId, file, {
          onProgress: (progress) => {
            console.log('Upload progress:', progress.progress)
            setUploadProgress(progress.progress)
          },
          onError: (error) => {
            console.error('Upload error:', error)
            setError(error.message)
          }
        })
      } else {
        throw new Error('Invalid bucket or missing required parameters')
      }

      // Update preview to use the uploaded URL
      setPreview(downloadURL)
      onUpload(downloadURL)
      setUploadProgress(100)
    } catch (error) {
      console.error('Upload error:', error)
      setError(error instanceof Error ? error.message : 'Upload failed')
      // Reset preview on error
      setPreview(currentImage || null)
    } finally {
      setUploading(false)
      // Reset progress after a delay
      setTimeout(() => setUploadProgress(0), 2000)
    }
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={className}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      <div
        onClick={handleClick}
        className="relative border-2 border-dashed border-earth-300 rounded-lg p-6 hover:border-accent-600 transition-colors cursor-pointer group"
      >
        {preview ? (
          <div className="relative">
            <div className="relative w-full h-48 rounded-lg overflow-hidden">
              <Image
                src={preview}
                alt={alt}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
            {uploading && (
              <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                  <p className="text-sm">{Math.round(uploadProgress)}%</p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center">
            <svg className="mx-auto h-12 w-12 text-earth-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
            </svg>
            <div className="mt-4">
              <p className="text-sm text-earth-600">
                {uploading ? `Uploading... ${Math.round(uploadProgress)}%` : 'Click to upload an image'}
              </p>
              <p className="text-xs text-earth-500 mt-1">
                PNG, JPG, GIF up to {maxSize}MB
              </p>
            </div>
          </div>
        )}
      </div>

      {error && (
        <p className="text-red-600 text-sm mt-2">{error}</p>
      )}
    </div>
  )
}
