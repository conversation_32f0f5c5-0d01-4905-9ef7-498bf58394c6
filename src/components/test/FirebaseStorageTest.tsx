'use client'

import { useState } from 'react'
import Image from 'next/image'
import { farmImageService } from '@/lib/firebase/storage'
import { useAuth } from '@/hooks/useAuth'

export function FirebaseStorageTest() {
  const { user } = useAuth()
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !user) return

    setUploading(true)
    setError(null)
    setUploadProgress(0)

    try {
      // Test farm image upload
      const downloadURL = await farmImageService.uploadFarmImage(user.uid, file, {
        onProgress: (progress) => {
          setUploadProgress(progress.progress)
        },
        onError: (error) => {
          setError(error.message)
        }
      })

      setUploadedUrl(downloadURL)
      console.log('✅ Upload successful:', downloadURL)
    } catch (error) {
      console.error('❌ Upload failed:', error)
      setError(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setUploading(false)
    }
  }

  if (!user) {
    return (
      <div className="p-4 border rounded-lg bg-yellow-50">
        <p className="text-yellow-800">Please log in to test Firebase Storage</p>
      </div>
    )
  }

  return (
    <div className="p-6 border rounded-lg bg-white shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Firebase Storage Test</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Test Image Upload
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            disabled={uploading}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>

        {uploading && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Uploading...</span>
              <span>{Math.round(uploadProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        )}

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {uploadedUrl && (
          <div className="space-y-2">
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-800 text-sm">✅ Upload successful!</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Uploaded Image:</p>
              <div className="relative w-64 h-48">
                <Image
                  src={uploadedUrl}
                  alt="Uploaded test image"
                  fill
                  className="object-cover rounded-lg border"
                  sizes="256px"
                />
              </div>
              <p className="text-xs text-gray-500 break-all">{uploadedUrl}</p>
            </div>
          </div>
        )}

        <div className="pt-4 border-t">
          <h4 className="font-medium text-sm mb-2">Test Status:</h4>
          <ul className="text-sm space-y-1">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
              Firebase Storage service initialized
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
              User authenticated: {user.email}
            </li>
            <li className="flex items-center">
              <span className={`w-2 h-2 rounded-full mr-2 ${uploadedUrl ? 'bg-green-500' : 'bg-gray-300'}`}></span>
              File upload test: {uploadedUrl ? 'Passed' : 'Pending'}
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}
