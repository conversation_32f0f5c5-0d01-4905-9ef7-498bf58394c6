'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { LocationAutocomplete, LocationDisplay } from '@/components/ui/LocationAutocomplete'
import { SearchBar, SearchFilters } from '@/components/features/SearchBar'
import { LocationData } from '@/lib/types/location'
import { getLocationService } from '@/lib/services/location'
import { DistanceService } from '@/lib/services/location/distance'

/**
 * Test component for location services functionality
 * This component helps verify that all location-related features work correctly
 */
export const LocationServiceTest: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null)
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [testResults, setTestResults] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const locationService = getLocationService()

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const handleLocationSelect = (locationData: LocationData) => {
    setSelectedLocation(locationData)
    addTestResult(`Location selected: ${locationData.formattedAddress}`)
    
    // Log detailed location data
    console.log('Selected location data:', locationData)
  }

  const handleSearch = (filters: SearchFilters) => {
    addTestResult(`Search triggered with query: "${filters.query}", location: ${filters.locationData?.formattedAddress || 'none'}, radius: ${filters.radius || 'any'}`)
    console.log('Search filters:', filters)
  }

  const testBrowserLocation = async () => {
    setIsLoading(true)
    addTestResult('Testing browser geolocation...')
    
    try {
      const result = await locationService.getCurrentLocation()
      if (result.success && result.data) {
        addTestResult(`Browser location: ${result.data.coords.latitude}, ${result.data.coords.longitude}`)
        
        // Test reverse geocoding
        const reverseResult = await locationService.reverseGeocode({
          lat: result.data.coords.latitude,
          lng: result.data.coords.longitude
        })
        
        if (reverseResult.success && reverseResult.data) {
          addTestResult(`Reverse geocoded to: ${reverseResult.data.formattedAddress}`)
        }
      } else {
        addTestResult(`Browser location failed: ${result.error?.message || 'Unknown error'}`)
      }
    } catch (error) {
      addTestResult(`Browser location error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testDistanceCalculation = () => {
    if (!selectedLocation) {
      addTestResult('Please select a location first to test distance calculation')
      return
    }

    // Test distance to major SA cities
    const testCities = [
      { name: 'Cape Town', lat: -33.9249, lng: 18.4241 },
      { name: 'Johannesburg', lat: -26.2041, lng: 28.0473 },
      { name: 'Durban', lat: -29.8587, lng: 31.0218 },
      { name: 'Pretoria', lat: -25.7479, lng: 28.2293 }
    ]

    const selectedCoords = {
      lat: selectedLocation.coordinates.latitude,
      lng: selectedLocation.coordinates.longitude
    }

    testCities.forEach(city => {
      const distance = DistanceService.calculateDistance(selectedCoords, city)
      const travelTime = DistanceService.estimateTravelTime(distance)
      addTestResult(`Distance to ${city.name}: ${distance.toFixed(1)}km (${travelTime.duration})`)
    })
  }

  const testPlaceSearch = async () => {
    setIsLoading(true)
    addTestResult('Testing place search for "Lephalale"...')
    
    try {
      const result = await locationService.getPlacePredictions('Lephalale')
      if (result.success && result.data) {
        addTestResult(`Found ${result.data.length} places for "Lephalale"`)
        setSearchResults(result.data.slice(0, 3)) // Show first 3 results
      } else {
        addTestResult(`Place search failed: ${result.error?.message || 'Unknown error'}`)
      }
    } catch (error) {
      addTestResult(`Place search error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const clearResults = () => {
    setTestResults([])
    setSearchResults([])
    setSelectedLocation(null)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Location Services Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Location Autocomplete Test */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Location Autocomplete</h3>
            <LocationAutocomplete
              onLocationSelect={handleLocationSelect}
              placeholder="Search for a location in South Africa..."
              className="max-w-md"
            />
            
            {selectedLocation && (
              <div className="mt-3">
                <LocationDisplay 
                  locationData={selectedLocation} 
                  showFullAddress={true}
                  className="text-sm"
                />
                <div className="mt-2 text-xs text-gray-600">
                  <p>Coordinates: {selectedLocation.coordinates.latitude.toFixed(6)}, {selectedLocation.coordinates.longitude.toFixed(6)}</p>
                  <p>Province: {selectedLocation.addressComponents.administrativeAreaLevel1}</p>
                  <p>City: {selectedLocation.addressComponents.locality}</p>
                </div>
              </div>
            )}
          </div>

          {/* Search Bar Test */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Enhanced Search Bar</h3>
            <SearchBar
              onSearch={handleSearch}
              placeholder="Test search functionality..."
              className="max-w-2xl"
            />
          </div>

          {/* Test Buttons */}
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={testBrowserLocation}
              disabled={isLoading}
              variant="outline"
            >
              Test Browser Location
            </Button>
            
            <Button 
              onClick={testDistanceCalculation}
              disabled={!selectedLocation}
              variant="outline"
            >
              Test Distance Calculation
            </Button>
            
            <Button 
              onClick={testPlaceSearch}
              disabled={isLoading}
              variant="outline"
            >
              Test Place Search
            </Button>
            
            <Button 
              onClick={clearResults}
              variant="outline"
            >
              Clear Results
            </Button>
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div>
              <h4 className="font-semibold mb-2">Search Results:</h4>
              <div className="space-y-2">
                {searchResults.map((result, index) => (
                  <div key={index} className="p-2 bg-gray-50 rounded text-sm">
                    <div className="font-medium">{result.mainText}</div>
                    <div className="text-gray-600">{result.secondaryText}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Test Results Log */}
          {testResults.length > 0 && (
            <div>
              <h4 className="font-semibold mb-2">Test Results:</h4>
              <div className="bg-gray-100 p-3 rounded max-h-60 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono mb-1">
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
