@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Design Guide Brand Colors */
  --dark-charcoal: #1c1c1c;
  --off-white: #ffffff;
  --cream: #eae6da;
  --forest-green: #3a4f41;
  --deep-brown: #8a4b2f;
  --warm-brown: #7a5c42;
  --light-gray: #7d7f7d;
  --dark-text: #0f0f0f;

  /* Legacy color mappings for existing functionality */
  --primary-brown: #8a4b2f;
  --primary-green: #3a4f41;
  --white: #ffffff;
  --medium-gray: #7d7f7d;
  --dark-gray: #1c1c1c;
  --black: #0f0f0f;

  /* Additional colors for existing features */
  --hunting-accent: #8a4b2f;
  --photo-accent: #3a4f41;
  --secondary-sunset: #8a4b2f;
  --secondary-cream: #eae6da;

  /* Typography - Design Guide Specifications */
  --font-display: 'YAFdJsjwyrY', 'Times New Roman', serif;
  --font-body: 'YAGL3rre3cA', 'Arial', sans-serif;
  --font-ui: 'YAGL3rre3cA', 'Arial', sans-serif;

  /* Typography Scale - Design Guide Sizes */
  --text-hero: 72.09px;
  --text-section-header: 66.76px;
  --text-large-heading: 62.76px;
  --text-navigation: 42.67px;
  --text-primary-body: 24.09px;
  --text-secondary-body: 18.51px;
  --text-labels: 18.67px;
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-xxl: 3rem;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.07), 0 1px 3px rgba(0,0,0,0.06);
  --shadow-lg: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
  --shadow-xl: 0 20px 25px rgba(0,0,0,0.15), 0 10px 10px rgba(0,0,0,0.04);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-body);
  line-height: 1.5;
  color: var(--dark-text);
  background: var(--off-white);
}

/* Design Guide Typography Classes */
.hero-title {
  font-family: var(--font-display);
  font-size: var(--text-hero);
  font-weight: 700;
  line-height: 1.4;
  letter-spacing: 0em;
  font-kerning: normal;
}

.section-header {
  font-family: var(--font-display);
  font-size: var(--text-section-header);
  font-weight: 700;
  line-height: 1.4;
  letter-spacing: 0em;
  font-kerning: normal;
}

.large-heading {
  font-family: var(--font-body);
  font-size: var(--text-large-heading);
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: 0em;
}

.nav-text {
  font-family: var(--font-body);
  font-size: var(--text-navigation);
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: 0em;
}

.body-primary {
  font-family: var(--font-body);
  font-size: var(--text-primary-body);
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0em;
}

.body-secondary {
  font-family: var(--font-body);
  font-size: var(--text-secondary-body);
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0em;
}

/* Enhanced heading styles with design guide typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 700;
  line-height: 1.4;
  margin-bottom: var(--space-md);
  letter-spacing: 0em;
  font-kerning: normal;
}

h1 {
  font-size: var(--text-hero);
  color: var(--forest-green);
}

h2 {
  font-size: var(--text-section-header);
  color: var(--forest-green);
}

h3 {
  font-size: var(--text-large-heading);
  color: var(--forest-green);
}

h4 {
  font-size: var(--text-primary-body);
  color: var(--dark-text);
}

h5 {
  font-size: var(--text-secondary-body);
  color: var(--dark-text);
}

h6 {
  font-size: var(--text-labels);
  color: var(--dark-text);
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  font-family: var(--font-ui);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.slide-up:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* Utility classes */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mobile responsive - Scale down design guide sizes proportionally */
@media (max-width: 768px) {
  :root {
    --text-hero: 48px;
    --text-section-header: 44px;
    --text-large-heading: 40px;
    --text-navigation: 28px;
    --text-primary-body: 18px;
    --text-secondary-body: 16px;
    --text-labels: 14px;
  }

  h1 { font-size: var(--text-hero); }
  h2 { font-size: var(--text-section-header); }
  h3 { font-size: var(--text-large-heading); }
  h4 { font-size: var(--text-primary-body); }
  h5 { font-size: var(--text-secondary-body); }
  h6 { font-size: var(--text-labels); }
}
