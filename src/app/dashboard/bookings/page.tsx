'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { bookingService, farmService } from '@/lib/firebase/firestore'
import { Booking, GameFarm } from '@/lib/types/firestore'
import { ArrowLeft, Calendar, MapPin, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import Link from 'next/link'

interface BookingWithFarm extends Booking {
  farm?: GameFarm
}

export default function BookingsPage() {
  const { user, userProfile, loading: authLoading } = useAuth()
  const router = useRouter()
  const [bookings, setBookings] = useState<BookingWithFarm[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchBookings = async () => {
      if (!userProfile?.id) return

      try {
        setLoading(true)
        setError(null)

        let userBookings: Booking[] = []

        if (userProfile.role === 'farm_owner') {
          // For farm owners, get bookings for their farms
          const userFarms = await farmService.getByOwner(userProfile.id)
          const farmIds = userFarms.map(farm => farm.id)

          if (farmIds.length > 0) {
            // Fetch bookings for each farm separately to respect security rules
            for (const farmId of farmIds) {
              try {
                const farmBookings = await bookingService.getAll({ farmId })
                userBookings.push(...farmBookings)
              } catch (error) {
                console.error(`Error fetching bookings for farm ${farmId}:`, error)
              }
            }
          }
        } else {
          // For guests, get their own bookings using filters
          userBookings = await bookingService.getAll({
            hunterId: userProfile.id
          })
        }

        // Fetch farm details for each booking
        const bookingsWithFarms: BookingWithFarm[] = await Promise.all(
          userBookings.map(async (booking) => {
            try {
              const farm = await farmService.get(booking.farmId)
              return { ...booking, farm: farm || undefined }
            } catch (err) {
              console.error(`Error fetching farm ${booking.farmId}:`, err)
              return booking
            }
          })
        )

        setBookings(bookingsWithFarms)
      } catch (err) {
        console.error('Error fetching bookings:', err)
        setError('Failed to load bookings')
      } finally {
        setLoading(false)
      }
    }

    if (!authLoading && userProfile) {
      fetchBookings()
    }
  }, [authLoading, userProfile])

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/login')
    }
  }, [authLoading, user, router])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'pending':
        return <AlertCircle className="w-5 h-5 text-yellow-600" />
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-600" />
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-blue-600" />
      default:
        return <Clock className="w-5 h-5 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-earth-100 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!user) {
    return null // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-earth-100 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          <div>
            <h1 className="text-3xl font-bold text-earth-900">
              {userProfile?.role === 'farm_owner' ? 'Booking Requests' : 'My Bookings'}
            </h1>
            <p className="text-earth-600 mt-2">
              {userProfile?.role === 'farm_owner' 
                ? 'Manage booking requests for your farms'
                : 'View and manage your safari bookings'
              }
            </p>
          </div>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Bookings List */}
        {bookings.length === 0 ? (
          <Card className="p-8">
            <div className="text-center">
              <Calendar className="w-16 h-16 text-earth-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-earth-900 mb-2">
                {userProfile?.role === 'farm_owner' ? 'No booking requests yet' : 'No bookings yet'}
              </h3>
              <p className="text-earth-600 mb-6">
                {userProfile?.role === 'farm_owner' 
                  ? 'When guests book your farms, their requests will appear here.'
                  : 'Start exploring our amazing farms and book your next safari adventure.'
                }
              </p>
              {userProfile?.role !== 'farm_owner' && (
                <Link href="/farms">
                  <Button variant="primary">Browse Farms</Button>
                </Link>
              )}
            </div>
          </Card>
        ) : (
          <div className="space-y-6">
            {bookings.map((booking) => (
              <Card key={booking.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        {getStatusIcon(booking.status)}
                        <h3 className="text-lg font-semibold text-earth-900">
                          {booking.farm?.name || 'Unknown Farm'}
                        </h3>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(booking.status)}`}>
                          {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-earth-600">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          <span>
                            {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          <span>{booking.durationDays} days</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          <span>{booking.farm?.location || 'Unknown location'}</span>
                        </div>
                        <div>
                          <span className="font-medium">
                            {booking.groupSize} {booking.groupSize === 1 ? 'guest' : 'guests'}
                          </span>
                        </div>
                      </div>

                      <div className="mt-3 flex items-center gap-4">
                        <span className="text-sm text-earth-600">
                          Activity: <span className="font-medium capitalize">{booking.activityType.replace('_', ' ')}</span>
                        </span>
                        <span className="text-sm text-earth-600">
                          Booking ID: <span className="font-mono">{booking.bookingReference}</span>
                        </span>
                      </div>

                      {booking.specialRequests && (
                        <div className="mt-3">
                          <p className="text-sm text-earth-600">
                            <span className="font-medium">Special Requests:</span> {booking.specialRequests}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="text-right ml-6">
                      {booking.totalPrice && (
                        <p className="text-xl font-bold text-earth-900 mb-2">
                          R{booking.totalPrice.toLocaleString()}
                        </p>
                      )}
                      <p className="text-sm text-earth-600 mb-3">
                        Booked on {booking.createdAt.toDate().toLocaleDateString()}
                      </p>
                      
                      {userProfile?.role === 'farm_owner' && booking.status === 'pending' && (
                        <div className="space-y-2">
                          <Button variant="primary" size="sm" className="w-full">
                            Accept
                          </Button>
                          <Button variant="outline" size="sm" className="w-full">
                            Decline
                          </Button>
                        </div>
                      )}
                      
                      {booking.farm && (
                        <Link href={`/farms/${booking.farm.id}`}>
                          <Button variant="outline" size="sm" className="w-full mt-2">
                            View Farm
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
