'use client'

import { useState, useEffect, useCallback } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { farmService } from '@/lib/firebase/firestore'
import { useAuth } from '@/hooks/useAuth'
import { GameFarm, ActivityType } from '@/lib/types/firestore'
import { SOUTH_AFRICAN_PROVINCES, SouthAfricanProvince } from '@/lib/constants'
import { ArrowLeft, Save, Eye } from 'lucide-react'
import Link from 'next/link'

interface FarmFormData {
  name: string
  description: string
  descriptionAfrikaans: string
  location: string
  province: SouthAfricanProvince | ''
  sizeHectares: string
  activityTypes: ActivityType
  contactEmail: string
  contactPhone: string
  websiteUrl: string
  rules: string
  rulesAfrikaans: string
  pricingInfo: string
  isActive: boolean
  featured: boolean
}



export default function EditFarmPage() {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const farmId = params.id as string

  const [farm, setFarm] = useState<GameFarm | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  const [formData, setFormData] = useState<FarmFormData>({
    name: '',
    description: '',
    descriptionAfrikaans: '',
    location: '',
    province: '',
    sizeHectares: '',
    activityTypes: 'both',
    contactEmail: '',
    contactPhone: '',
    websiteUrl: '',
    rules: '',
    rulesAfrikaans: '',
    pricingInfo: '',
    isActive: true,
    featured: false
  })

  const fetchFarm = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const farmData = await farmService.get(farmId)

      if (!farmData) {
        throw new Error('Farm not found')
      }

      // Ensure user owns this farm
      if (farmData.ownerId !== user!.uid) {
        throw new Error('Access denied - you do not own this farm')
      }

      setFarm(farmData)
      setFormData({
        name: farmData.name,
        description: farmData.description || '',
        descriptionAfrikaans: farmData.descriptionAfrikaans || '',
        location: farmData.location,
        province: farmData.province,
        sizeHectares: farmData.sizeHectares?.toString() || '',
        activityTypes: farmData.activityTypes,
        contactEmail: farmData.contactEmail,
        contactPhone: farmData.contactPhone || '',
        websiteUrl: farmData.websiteUrl || '',
        rules: farmData.rules || '',
        rulesAfrikaans: farmData.rulesAfrikaans || '',
        pricingInfo: farmData.pricingInfo || '',
        isActive: farmData.isActive,
        featured: farmData.featured
      })
    } catch (err) {
      console.error('Error fetching farm:', err)
      setError('Failed to load farm details')
    } finally {
      setLoading(false)
    }
  }, [farmId, user])

  useEffect(() => {
    if (!authLoading && user) {
      fetchFarm()
    }
  }, [authLoading, user, fetchFarm])

  const handleInputChange = (field: keyof FarmFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setSuccessMessage(null) // Clear success message when user makes changes
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user || !farm) return

    try {
      setSaving(true)
      setError(null)

      const updateData = {
        name: formData.name,
        description: formData.description || undefined,
        descriptionAfrikaans: formData.descriptionAfrikaans || undefined,
        location: formData.location,
        province: formData.province as SouthAfricanProvince,
        sizeHectares: formData.sizeHectares ? parseInt(formData.sizeHectares) : undefined,
        activityTypes: formData.activityTypes,
        contactEmail: formData.contactEmail,
        contactPhone: formData.contactPhone || undefined,
        websiteUrl: formData.websiteUrl || undefined,
        rules: formData.rules || undefined,
        rulesAfrikaans: formData.rulesAfrikaans || undefined,
        pricingInfo: formData.pricingInfo || undefined,
        isActive: formData.isActive,
        featured: formData.featured
      }

      await farmService.update(farmId, updateData)

      setSuccessMessage('Farm details updated successfully!')

      // Refresh farm data
      await fetchFarm()
    } catch (err) {
      console.error('Error updating farm:', err)
      setError('Failed to update farm details')
    } finally {
      setSaving(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-earth-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"></div>
          <p className="mt-4 text-earth-600">Loading farm details...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    router.push('/auth/login')
    return null
  }

  if (error && !farm) {
    return (
      <div className="min-h-screen bg-earth-100 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-earth-900 mb-4">Farm Not Found</h1>
            <p className="text-earth-600 mb-6">{error}</p>
            <Link href="/dashboard">
              <Button variant="primary">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-earth-100 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <Link href={`/farms/${farmId}`}>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-2" />
                View Public Page
              </Button>
            </Link>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-earth-900">Edit Farm Listing</h1>
              <p className="text-earth-600 mt-2">
                Update your farm details and settings
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={formData.isActive ? 'hunting' : 'default'}>
                {formData.isActive ? 'Active' : 'Inactive'}
              </Badge>
              {formData.featured && (
                <Badge variant="photo">Featured</Badge>
              )}
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {successMessage && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <p className="text-green-600 text-sm">{successMessage}</p>
            </div>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-earth-700 mb-2">
                  Farm Name *
                </label>
                <Input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter your farm name"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-earth-700 mb-2">
                    Location/City *
                  </label>
                  <Input
                    id="location"
                    type="text"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="e.g., Lephalale"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="province" className="block text-sm font-medium text-earth-700 mb-2">
                    Province *
                  </label>
                  <select
                    id="province"
                    value={formData.province}
                    onChange={(e) => handleInputChange('province', e.target.value)}
                    className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                    required
                  >
                    <option value="">Select Province</option>
                    {SOUTH_AFRICAN_PROVINCES.map(province => (
                      <option key={province} value={province}>{province}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="sizeHectares" className="block text-sm font-medium text-earth-700 mb-2">
                  Farm Size (Hectares)
                </label>
                <Input
                  id="sizeHectares"
                  type="number"
                  value={formData.sizeHectares}
                  onChange={(e) => handleInputChange('sizeHectares', e.target.value)}
                  placeholder="e.g., 5000"
                />
              </div>

              <div>
                <label htmlFor="activityTypes" className="block text-sm font-medium text-earth-700 mb-2">
                  Activity Types *
                </label>
                <select
                  id="activityTypes"
                  value={formData.activityTypes}
                  onChange={(e) => handleInputChange('activityTypes', e.target.value as ActivityType)}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                  required
                >
                  <option value="hunting">Hunting Only</option>
                  <option value="photo_safari">Photo Safari Only</option>
                  <option value="both">Both Hunting & Photo Safari</option>
                </select>
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-earth-700 mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe your farm, facilities, and what makes it special..."
                  rows={4}
                  className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="pricingInfo" className="block text-sm font-medium text-earth-700 mb-2">
                  Pricing Information
                </label>
                <Input
                  id="pricingInfo"
                  type="text"
                  value={formData.pricingInfo}
                  onChange={(e) => handleInputChange('pricingInfo', e.target.value)}
                  placeholder="e.g., From R2,500 per day"
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="contactEmail" className="block text-sm font-medium text-earth-700 mb-2">
                    Contact Email *
                  </label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={formData.contactEmail}
                    onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="contactPhone" className="block text-sm font-medium text-earth-700 mb-2">
                    Contact Phone
                  </label>
                  <Input
                    id="contactPhone"
                    type="tel"
                    value={formData.contactPhone}
                    onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                    placeholder="+27 12 345 6789"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="websiteUrl" className="block text-sm font-medium text-earth-700 mb-2">
                  Website URL
                </label>
                <Input
                  id="websiteUrl"
                  type="text"
                  value={formData.websiteUrl}
                  onChange={(e) => handleInputChange('websiteUrl', e.target.value)}
                  placeholder="www.yourfarm.com"
                />
              </div>
            </CardContent>
          </Card>

          {/* Farm Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Farm Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-earth-700">Active Status</h4>
                  <p className="text-sm text-earth-600">Control whether your farm is visible to customers</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-accent-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-earth-700">Featured Listing</h4>
                  <p className="text-sm text-earth-600">Featured farms appear at the top of search results</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.featured}
                    onChange={(e) => handleInputChange('featured', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-accent-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent-600"></div>
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end gap-4">
            <Link href="/dashboard">
              <Button variant="outline" type="button">
                Cancel
              </Button>
            </Link>
            <Button
              type="submit"
              variant="primary"
              disabled={saving}
              className="min-w-[120px]"
            >
              {saving ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </div>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
