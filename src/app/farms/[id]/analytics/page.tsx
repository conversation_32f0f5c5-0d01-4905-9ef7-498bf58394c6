'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { farmService, bookingService, reviewService } from '@/lib/firebase/firestore'
import { useAuth } from '@/hooks/useAuth'
import { GameFarm } from '@/lib/types/firestore'
import { ArrowLeft, TrendingUp, Calendar, DollarSign, Eye, Star } from 'lucide-react'
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, LineChart, Line, PieChart, Pie, Cell } from 'recharts'
import Link from 'next/link'
import { formatNumber } from '@/lib/utils'

interface FarmAnalytics {
  totalBookings: number
  totalRevenue: number
  averageBookingValue: number
  totalViews: number
  conversionRate: number
  averageRating: number
  reviewCount: number
  bookingsByMonth: { month: string; bookings: number; revenue: number }[]
  bookingsByStatus: { status: string; count: number; color: string }[]
  activityBreakdown: { activity: string; count: number; color: string }[]
}

const COLORS = {
  primary: '#A0522D',
  hunting: '#8B4513',
  photo: '#228B22',
  pending: '#FFA500',
  confirmed: '#32CD32',
  completed: '#4169E1',
  cancelled: '#DC143C'
}

export default function FarmAnalyticsPage() {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const farmId = params.id as string

  const [farm, setFarm] = useState<GameFarm | null>(null)
  const [analytics, setAnalytics] = useState<FarmAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState<'30d' | '90d' | '1y' | 'all'>('90d')

  const fetchAnalytics = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch farm details
      const farmData = await farmService.get(farmId)

      if (!farmData) {
        throw new Error('Farm not found')
      }

      // Ensure user owns this farm
      if (farmData.ownerId !== user!.uid) {
        throw new Error('Access denied - you do not own this farm')
      }

      setFarm(farmData)

      // Calculate date range
      const now = new Date()
      let startDate = new Date()
      switch (timeRange) {
        case '30d':
          startDate.setDate(now.getDate() - 30)
          break
        case '90d':
          startDate.setDate(now.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(now.getFullYear() - 1)
          break
        case 'all':
          startDate = new Date('2020-01-01') // Far back date
          break
      }

      // Fetch bookings for analytics
      const bookingsData = await bookingService.getAll({ farmId })

      // Filter bookings by date range
      const filteredBookings = bookingsData.filter(booking => {
        const bookingDate = booking.createdAt.toDate()
        return bookingDate >= startDate
      })

      // Fetch reviews for rating analytics
      const reviewsData = await reviewService.getByFarm(farmId)

      // Process analytics data
      const bookings = filteredBookings || []
      const reviews = reviewsData || []

      const totalBookings = bookings.length
      const totalRevenue = bookings
        .filter(b => b.status === 'completed')
        .reduce((sum, b) => sum + (b.totalPrice || 0), 0)
      const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0
      const totalViews = 0 // TODO: Implement view tracking
      const conversionRate = totalViews > 0 ? (totalBookings / totalViews) * 100 : 0
      const averageRating = reviews.length > 0
        ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
        : 0

      // Bookings by month
      const monthlyData = new Map<string, { bookings: number; revenue: number }>()
      bookings.forEach(booking => {
        const month = booking.createdAt.toDate().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short'
        })
        const current = monthlyData.get(month) || { bookings: 0, revenue: 0 }
        monthlyData.set(month, {
          bookings: current.bookings + 1,
          revenue: current.revenue + (booking.status === 'completed' ? (booking.totalPrice || 0) : 0)
        })
      })

      const bookingsByMonth = Array.from(monthlyData.entries())
        .map(([month, data]) => ({ month, ...data }))
        .sort((a, b) => new Date(a.month).getTime() - new Date(b.month).getTime())

      // Bookings by status
      const statusCounts = new Map<string, number>()
      bookings.forEach(booking => {
        statusCounts.set(booking.status, (statusCounts.get(booking.status) || 0) + 1)
      })

      const bookingsByStatus = Array.from(statusCounts.entries()).map(([status, count]) => ({
        status: status.charAt(0).toUpperCase() + status.slice(1),
        count,
        color: COLORS[status as keyof typeof COLORS] || COLORS.primary
      }))

      // Activity breakdown
      const activityCounts = new Map<string, number>()
      bookings.forEach(booking => {
        const activity = booking.activityType === 'hunting' ? 'Hunting' : 'Photo Safari'
        activityCounts.set(activity, (activityCounts.get(activity) || 0) + 1)
      })

      const activityBreakdown = Array.from(activityCounts.entries()).map(([activity, count]) => ({
        activity,
        count,
        color: activity === 'Hunting' ? COLORS.hunting : COLORS.photo
      }))

      setAnalytics({
        totalBookings,
        totalRevenue,
        averageBookingValue,
        totalViews,
        conversionRate,
        averageRating,
        reviewCount: reviews.length,
        bookingsByMonth,
        bookingsByStatus,
        activityBreakdown
      })

    } catch (err) {
      console.error('Error fetching analytics:', err)
      setError('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }, [farmId, user, timeRange])

  useEffect(() => {
    if (!authLoading && user) {
      fetchAnalytics()
    }
  }, [authLoading, user, fetchAnalytics])

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-earth-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"></div>
          <p className="mt-4 text-earth-600">Loading analytics...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    router.push('/auth/login')
    return null
  }

  if (error && !farm) {
    return (
      <div className="min-h-screen bg-earth-100 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-earth-900 mb-4">Analytics Not Available</h1>
            <p className="text-earth-600 mb-6">{error}</p>
            <Link href="/dashboard">
              <Button variant="primary">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-earth-100 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <Link href={`/farms/${farmId}`}>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-2" />
                View Public Page
              </Button>
            </Link>
            <Link href={`/farms/${farmId}/edit`}>
              <Button variant="outline" size="sm">
                Edit Farm
              </Button>
            </Link>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-earth-900">Farm Analytics</h1>
              <p className="text-earth-600 mt-2">
                {farm?.name} - Performance insights and metrics
              </p>
            </div>

            {/* Time Range Selector */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-earth-600">Time Range:</span>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as typeof timeRange)}
                className="px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
              >
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
                <option value="1y">Last Year</option>
                <option value="all">All Time</option>
              </select>
            </div>
          </div>
        </div>

        {analytics && (
          <div className="space-y-8">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="text-center py-6">
                  <div className="flex flex-col items-center">
                    <Calendar className="text-accent-600 mb-2" size={24} />
                    <div className="text-3xl font-bold text-accent-600 mb-1">
                      {analytics.totalBookings}
                    </div>
                    <p className="text-earth-600 text-sm">Total Bookings</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="text-center py-6">
                  <div className="flex flex-col items-center">
                    <DollarSign className="text-hunting-accent mb-2" size={24} />
                    <div className="text-3xl font-bold text-hunting-accent mb-1">
                      R{formatNumber(analytics.totalRevenue)}
                    </div>
                    <p className="text-earth-600 text-sm">Total Revenue</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="text-center py-6">
                  <div className="flex flex-col items-center">
                    <TrendingUp className="text-photo-accent mb-2" size={24} />
                    <div className="text-3xl font-bold text-photo-accent mb-1">
                      R{formatNumber(analytics.averageBookingValue)}
                    </div>
                    <p className="text-earth-600 text-sm">Avg. Booking Value</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="text-center py-6">
                  <div className="flex flex-col items-center">
                    <Star className="text-accent-600 mb-2" size={24} />
                    <div className="text-3xl font-bold text-accent-600 mb-1">
                      {analytics.averageRating > 0 ? analytics.averageRating.toFixed(1) : 'N/A'}
                    </div>
                    <p className="text-earth-600 text-sm">
                      Avg. Rating ({analytics.reviewCount} reviews)
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Charts Row 1 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Bookings Over Time */}
              <Card>
                <CardHeader>
                  <CardTitle>Bookings Over Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={analytics.bookingsByMonth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" tick={{ fontSize: 12 }} />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip />
                      <Line
                        type="monotone"
                        dataKey="bookings"
                        stroke={COLORS.primary}
                        strokeWidth={2}
                        dot={{ fill: COLORS.primary }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Revenue Over Time */}
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Over Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={analytics.bookingsByMonth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" tick={{ fontSize: 12 }} />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip formatter={(value) => `R${Number(value).toLocaleString()}`} />
                      <Bar dataKey="revenue" fill={COLORS.hunting} radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Charts Row 2 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Booking Status Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Booking Status Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={analytics.bookingsByStatus}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ status, count }) => `${status}: ${count}`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {analytics.bookingsByStatus.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Activity Type Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Activity Type Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={analytics.activityBreakdown}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ activity, count }) => `${activity}: ${count}`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {analytics.activityBreakdown.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
