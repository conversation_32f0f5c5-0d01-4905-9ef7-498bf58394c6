import { Shield, AlertTriangle, Heart, Eye, Target, Camera } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card'

const safetyCategories = [
  {
    icon: Target,
    title: 'Hunting Safety',
    color: 'hunting-accent',
    guidelines: [
      'Always treat firearms as if they are loaded',
      'Never point a firearm at anything you do not intend to shoot',
      'Keep your finger off the trigger until ready to shoot',
      'Be sure of your target and what is beyond it',
      'Wear appropriate safety gear including eye and ear protection',
      'Follow all instructions from your professional hunter guide',
      'Maintain proper firearm handling and storage protocols',
      'Ensure you have valid hunting licenses and permits'
    ]
  },
  {
    icon: Camera,
    title: 'Photo Safari Safety',
    color: 'photo-accent',
    guidelines: [
      'Maintain safe distances from all wildlife',
      'Never exit vehicles without guide permission',
      'Follow all instructions from your guide immediately',
      'Keep noise levels low to avoid disturbing animals',
      'Secure all equipment to prevent dropping items',
      'Wear neutral-colored clothing to blend with environment',
      'Stay hydrated and protect yourself from sun exposure',
      'Respect animal behavior and retreat if animals show stress'
    ]
  }
]

const generalSafety = [
  {
    icon: Shield,
    title: 'General Precautions',
    items: [
      'Inform someone of your travel plans and expected return',
      'Carry emergency contact information at all times',
      'Follow all farm rules and regulations strictly',
      'Stay within designated areas unless accompanied by guides',
      'Report any safety concerns immediately to farm management'
    ]
  },
  {
    icon: Heart,
    title: 'Health & Medical',
    items: [
      'Ensure you have comprehensive travel and medical insurance',
      'Bring any required medications and prescriptions',
      'Inform guides of any medical conditions or allergies',
      'Stay hydrated and protect yourself from sun exposure',
      'Be aware of potential disease vectors (malaria, tick-borne diseases)'
    ]
  },
  {
    icon: AlertTriangle,
    title: 'Emergency Preparedness',
    items: [
      'Know the location of first aid facilities',
      'Understand emergency evacuation procedures',
      'Keep emergency contact numbers readily available',
      'Carry a charged mobile phone when possible',
      'Follow all emergency instructions from guides and staff'
    ]
  }
]

const equipmentSafety = [
  'Inspect all equipment before use',
  'Use only equipment provided or approved by the farm',
  'Report any damaged or malfunctioning equipment immediately',
  'Follow proper maintenance and storage procedures',
  'Ensure all safety equipment is properly fitted and functional'
]

const wildlifeSafety = [
  'Never approach or attempt to feed wild animals',
  'Maintain respectful distances as advised by guides',
  'Move slowly and avoid sudden movements around animals',
  'Never come between animals and their escape routes',
  'Be especially cautious around mothers with young',
  'Understand animal warning signs and behaviors',
  'Follow guide instructions for animal encounters immediately'
]

export default function SafetyPage() {
  return (
    <div className="min-h-screen bg-earth-100">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-earth-600 to-earth-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Shield className="w-16 h-16 mx-auto mb-6 text-white" />
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Safety Guidelines</h1>
          <p className="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
            Your safety is our top priority. Please review these important guidelines before your safari experience.
          </p>
        </div>
      </section>

      {/* Activity-Specific Safety */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-earth-900 mb-12 text-center">Activity-Specific Safety</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {safetyCategories.map((category, index) => (
              <Card key={index} className="h-full">
                <CardHeader>
                  <div className="flex items-center mb-4">
                    <div className={`w-12 h-12 bg-${category.color} bg-opacity-10 rounded-lg flex items-center justify-center mr-4`}>
                      <category.icon className={`w-6 h-6 text-${category.color}`} />
                    </div>
                    <CardTitle className="text-2xl">{category.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {category.guidelines.map((guideline, guidelineIndex) => (
                      <li key={guidelineIndex} className="flex items-start">
                        <div className={`w-2 h-2 bg-${category.color} rounded-full mt-2 mr-3 flex-shrink-0`}></div>
                        <span className="text-earth-700">{guideline}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* General Safety */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-earth-900 mb-12 text-center">General Safety Guidelines</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {generalSafety.map((category, index) => (
              <Card key={index} className="h-full">
                <CardHeader>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mr-4">
                      <category.icon className="w-6 h-6 text-accent-600" />
                    </div>
                    <CardTitle className="text-xl">{category.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {category.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-accent-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-earth-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Wildlife Safety */}
      <section className="py-16 bg-earth-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Eye className="w-16 h-16 mx-auto mb-6 text-accent-600" />
            <h2 className="text-3xl font-bold text-earth-900 mb-4">Wildlife Safety</h2>
            <p className="text-lg text-earth-700">
              Respect for wildlife is essential for both your safety and conservation efforts.
            </p>
          </div>
          
          <Card>
            <CardContent className="p-8">
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {wildlifeSafety.map((rule, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-accent-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-earth-700">{rule}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Equipment Safety */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-earth-900 mb-4">Equipment Safety</h2>
            <p className="text-lg text-earth-700">
              Proper equipment handling is crucial for a safe safari experience.
            </p>
          </div>
          
          <Card>
            <CardContent className="p-8">
              <ul className="space-y-4">
                {equipmentSafety.map((rule, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-accent-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-earth-700">{rule}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Emergency Information */}
      <section className="py-16 bg-red-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <AlertTriangle className="w-16 h-16 mx-auto mb-6 text-red-600" />
            <h2 className="text-3xl font-bold text-red-900 mb-4">Emergency Information</h2>
          </div>
          
          <Card className="border-red-200">
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold text-red-900 mb-4">Emergency Contacts</h3>
                  <div className="space-y-2 text-earth-700">
                    <p><strong>Emergency Services:</strong> 10111</p>
                    <p><strong>Medical Emergency:</strong> 10177</p>
                    <p><strong>BvR Safaris Emergency:</strong> +27 (0)82 123 4567</p>
                    <p><strong>Farm Emergency:</strong> Contact your farm operator</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-red-900 mb-4">In Case of Emergency</h3>
                  <ol className="list-decimal list-inside space-y-2 text-earth-700">
                    <li>Ensure immediate safety of all persons</li>
                    <li>Contact emergency services if required</li>
                    <li>Notify your guide or farm management</li>
                    <li>Contact BvR Safaris emergency line</li>
                    <li>Follow all instructions from authorities</li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Disclaimer */}
      <section className="py-8 bg-earth-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-sm opacity-90">
            <strong>Important:</strong> These guidelines are for general information only. Always follow specific instructions 
            from your farm operator and professional guides. Safari activities involve inherent risks, and participants 
            engage at their own risk. Comprehensive insurance coverage is strongly recommended.
          </p>
        </div>
      </section>
    </div>
  )
}
