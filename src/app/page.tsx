"use client"

import { useEffect, useState, useCallback } from 'react'
import { HeroImageLoader } from '@/components/ui/HeroImageLoader'
import { SearchBar } from '@/components/features/SearchBar'
import { FeaturedFarmsCarousel } from '@/components/features/FeaturedFarmsCarousel'
import { farmService } from '@/lib/firebase/firestore'
import { GameFarm } from '@/lib/types/firestore'
import { useRouter } from 'next/navigation'

interface FarmWithStats extends GameFarm {
  rating?: number
  reviewCount?: number
  activities: ('hunting' | 'photo_safari')[]
  imageUrl?: string
}

export default function Home() {
  const [featuredFarms, setFeaturedFarms] = useState<FarmWithStats[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchFeaturedFarms = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const farmsData = await farmService.getAll({
        featured: true,
        isActive: true,
        limit: 3
      })

      const processedFarms: FarmWithStats[] = farmsData.map((farm) => {
        // Convert activityTypes to activities array
        const activities: ('hunting' | 'photo_safari')[] = []
        if (farm.activityTypes === 'hunting' || farm.activityTypes === 'both') {
          activities.push('hunting')
        }
        if (farm.activityTypes === 'photo_safari' || farm.activityTypes === 'both') {
          activities.push('photo_safari')
        }

        return {
          ...farm,
          activities,
          rating: undefined, // TODO: Calculate from reviews subcollection
          reviewCount: 0, // TODO: Count from reviews subcollection
          imageUrl: '/globe.svg' // TODO: Get from images subcollection
        }
      })

      setFeaturedFarms(processedFarms)
    } catch (err) {
      console.error('Error fetching featured farms:', err)
      setError('Failed to load featured farms')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchFeaturedFarms()
  }, [fetchFeaturedFarms])

  const router = useRouter()

  const handleSearch = (query: string, location: string) => {
    const params = new URLSearchParams()
    if (query) params.set('search', query)
    if (location) params.set('location', location)
    router.push(`/farms?${params.toString()}`)
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section - Design Guide Layout with Left-Aligned Text */}
      <section className="relative text-white" style={{
        backgroundColor: 'var(--dark-charcoal)',
        height: '600px',
        minHeight: '600px'
      }}>
        <HeroImageLoader priority className="absolute inset-0">
          <div className="relative z-20 h-full flex items-center" style={{
            maxWidth: '995px',
            margin: '0 auto',
            padding: '0 60px'
          }}>
            {/* Left-aligned text container matching design mockup - moved further left */}
            <div className="text-left max-w-2xl" style={{ marginLeft: '-150px' }}>
              {/* Hero Title - Design Guide Typography with Design Mockup Text */}
              <h1 className="hero-title mb-6" style={{
                fontFamily: 'var(--font-display)',
                fontSize: 'var(--text-large-heading)',
                fontWeight: '400',
                color: 'var(--cream)',
                lineHeight: '1.4',
                letterSpacing: '0em'
              }}>
                The wild is calling,<br/>
                will you answer?
              </h1>

            </div>
          </div>
        </HeroImageLoader>
      </section>

      {/* Search Bar Section - Standalone Section Below Hero */}
      <section className="py-12" style={{ backgroundColor: 'var(--off-white)' }}>
        <div className="mx-auto" style={{ maxWidth: '995px', padding: '0 89px' }}>
          <div className="text-center mb-8">
            <h2 className="section-header mb-4" style={{
              fontFamily: 'var(--font-body)',
              fontSize: 'var(--text-primary-body)',
              fontWeight: '400',
              color: 'var(--deep-brown)',
              lineHeight: '1.4',
              letterSpacing: '0em'
            }}>
              Search
            </h2>
            <p className="body-secondary max-w-4xl mx-auto" style={{
              fontFamily: 'var(--font-body)',
              fontSize: 'var(--text-secondary-body)',
              color: 'var(--forest-green)',
              lineHeight: '1.5',
              letterSpacing: '0em'
            }}>
              Use our powerful search bar to quickly find the ideal hunting destination tailored to your needs.
              Simply enter a location, province, or game farm name to explore options in your preferred area. You
              can also filter your search by the type of game available—whether you&rsquo;re after plains game, dangerous
              game, or specific species like kudu, impala, or warthog. Refine your results further by selecting
              hunting methods (rifle, bow, or walk-and-stalk), accommodation type, group size, available dates, or
              budget range. Our intuitive filters make it easy to compare listings and find exactly what you&rsquo;re looking
              for, whether it&rsquo;s a weekend getaway or a full-scale hunting safari.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <SearchBar onSearch={handleSearch} />
          </div>
        </div>
      </section>

      {/* Featured Farms Carousel - Design Guide Styling */}
      {featuredFarms.length > 0 && (
        <section className="py-16" style={{ backgroundColor: 'var(--dark-charcoal)', minHeight: '593px' }}>
          <div className="mx-auto" style={{ maxWidth: '995px', padding: '0 89px' }}>
            <div className="text-center mb-12">
              <h2 className="section-header mb-6" style={{
                fontFamily: 'var(--font-display)',
                fontSize: 'var(--text-section-header)',
                fontWeight: '700',
                color: 'var(--cream)',
                lineHeight: '1.4',
                letterSpacing: '0em'
              }}>
                Featured Listings
              </h2>
              <p className="body-primary max-w-2xl mx-auto" style={{
                fontFamily: 'var(--font-body)',
                fontSize: 'var(--text-primary-body)',
                color: 'var(--cream)',
                lineHeight: '1.5',
                letterSpacing: '0em'
              }}>
                Use area as a carousel of recommended listings or specials.
              </p>
            </div>

            <FeaturedFarmsCarousel
              farms={featuredFarms}
              loading={loading}
              error={error}
              onRetry={fetchFeaturedFarms}
            />
          </div>
        </section>
      )}

      {/* Why Choose Us Section - Design Guide Styling */}
      <section className="py-16" style={{ backgroundColor: 'var(--off-white)', minHeight: '792px' }}>
        <div className="mx-auto" style={{ maxWidth: '995px', padding: '0 89px' }}>
          <div className="text-center mb-12">
            <h2 className="section-header mb-6" style={{
              fontFamily: 'var(--font-display)',
              fontSize: 'var(--text-section-header)',
              fontWeight: '700',
              color: 'var(--forest-green)',
              lineHeight: '1.4',
              letterSpacing: '0em'
            }}>
              Why choose us?
            </h2>
          </div>

          <div className="max-w-4xl mx-auto">
            <p className="body-primary text-center" style={{
              fontFamily: 'var(--font-body)',
              fontSize: 'var(--text-primary-body)',
              color: 'var(--dark-text)',
              lineHeight: '1.5',
              letterSpacing: '0em'
            }}>
              At BvR Safaris, we connect passionate hunters with premium hunting
              destinations across South Africa, offering a curated selection of lodges, farms,
              and outfitters that meet the highest standards of quality, safety, and ethical
              hunting practices. Whether you&rsquo;re seeking a self-catering bushveld getaway or
              a fully guided trophy hunt, our platform simplifies the booking process and
              ensures transparent pricing, verified reviews, and expert support every step of
              the way. With a focus on conservation, professionalism, and unforgettable
              outdoor experiences, we are your trusted partner in planning the perfect
              hunting escape.
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
