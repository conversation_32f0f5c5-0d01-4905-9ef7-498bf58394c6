import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  console.log('🔍 Places autocomplete API called')

  const { searchParams } = new URL(request.url)
  const input = searchParams.get('input')
  const sessionToken = searchParams.get('sessiontoken')
  const types = searchParams.get('types')

  console.log('📝 Request params:', { input, sessionToken, types })

  if (!input) {
    console.log('❌ No input provided')
    return NextResponse.json({ error: 'Input parameter is required' }, { status: 400 })
  }

  const apiKey = process.env.GOOGLE_PLACES_API_KEY_SERVER
  console.log('🔑 API key exists:', !!apiKey)

  if (!apiKey) {
    console.log('❌ No API key configured')
    return NextResponse.json({ error: 'Google Places API key not configured' }, { status: 500 })
  }

  try {
    // Use the new Places API (New) - Text Search (Autocomplete)
    const requestBody = {
      input,
      languageCode: 'en',
      regionCode: 'ZA', // South Africa
      includedRegionCodes: ['ZA'], // Restrict to South Africa
      sessionToken: sessionToken || undefined
    }

    // Note: includedPrimaryTypes is available but may need specific type values
    // For now, we'll rely on regionCode filtering

    const response = await fetch(
      `https://places.googleapis.com/v1/places:autocomplete`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-Api-Key': apiKey,
          'X-Goog-FieldMask': 'suggestions.placePrediction.placeId,suggestions.placePrediction.text,suggestions.placePrediction.structuredFormat,suggestions.placePrediction.types'
        },
        body: JSON.stringify(requestBody)
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Google Places API error: ${response.status} - ${errorText}`)
    }

    const data = await response.json()

    // Transform the response to match our expected format
    const predictions = data.suggestions?.map((suggestion: any) => {
      const prediction = suggestion.placePrediction
      if (!prediction) return null

      return {
        placeId: prediction.placeId,
        description: prediction.text?.text || '',
        mainText: prediction.structuredFormat?.mainText?.text || prediction.text?.text || '',
        secondaryText: prediction.structuredFormat?.secondaryText?.text || '',
        types: prediction.types || [],
      }
    }).filter(Boolean) || []

    return NextResponse.json({
      success: true,
      data: predictions
    })

  } catch (error) {
    console.error('Places autocomplete API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      },
      { status: 500 }
    )
  }
}
