import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const placeId = searchParams.get('place_id')
  const sessionToken = searchParams.get('sessiontoken')
  
  if (!placeId) {
    return NextResponse.json({ error: 'place_id parameter is required' }, { status: 400 })
  }

  const apiKey = process.env.GOOGLE_PLACES_API_KEY_SERVER
  if (!apiKey) {
    return NextResponse.json({ error: 'Google Places API key not configured' }, { status: 500 })
  }

  try {
    // Use the new Places API (New) - Place Details
    const response = await fetch(
      `https://places.googleapis.com/v1/places/${placeId}`,
      {
        method: 'GET',
        headers: {
          'X-Goog-Api-Key': apiKey,
          'X-Goog-FieldMask': 'id,displayName,formattedAddress,location,addressComponents,types,viewport'
        }
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Google Places API error: ${response.status} - ${errorText}`)
    }

    const place = await response.json()
    if (!place) {
      throw new Error('No place data returned')
    }

    // Transform address components from new API format
    const addressComponents: any = {}
    place.addressComponents?.forEach((component: any) => {
      const types = component.types
      if (types.includes('locality')) {
        addressComponents.locality = component.longText
      }
      if (types.includes('administrative_area_level_1')) {
        addressComponents.administrativeAreaLevel1 = component.longText
      }
      if (types.includes('administrative_area_level_2')) {
        addressComponents.administrativeAreaLevel2 = component.longText
      }
      if (types.includes('country')) {
        addressComponents.country = component.longText
        addressComponents.countryCode = component.shortText
      }
      if (types.includes('postal_code')) {
        addressComponents.postalCode = component.longText
      }
      if (types.includes('route')) {
        addressComponents.route = component.longText
      }
      if (types.includes('street_number')) {
        addressComponents.streetNumber = component.longText
      }
    })

    // Transform the response to match our expected format
    const placeDetails = {
      placeId: place.id,
      formattedAddress: place.formattedAddress,
      name: place.displayName?.text,
      coordinates: {
        latitude: place.location?.latitude || 0,
        longitude: place.location?.longitude || 0
      },
      addressComponents,
      types: place.types || [],
      viewport: place.viewport ? {
        northeast: {
          lat: place.viewport.high?.latitude || 0,
          lng: place.viewport.high?.longitude || 0
        },
        southwest: {
          lat: place.viewport.low?.latitude || 0,
          lng: place.viewport.low?.longitude || 0
        }
      } : undefined
    }

    return NextResponse.json({
      success: true,
      data: placeDetails
    })

  } catch (error) {
    console.error('Places details API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      },
      { status: 500 }
    )
  }
}
