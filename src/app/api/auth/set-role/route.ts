import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from '@/lib/types/firestore'

interface SetRoleRequest {
  uid: string
  role: UserRole
}

interface SetRoleResponse {
  success: boolean
  message?: string
  error?: string
}

// Get the Cloud Function URL based on environment
function getCloudFunctionURL(): string {
  const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID

  if (!projectId) {
    throw new Error('NEXT_PUBLIC_FIREBASE_PROJECT_ID environment variable is required')
  }

  if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATORS === 'true') {
    return `http://localhost:5001/${projectId}/us-central1/setUserRole`
  }

  // Production URL
  return `https://us-central1-${projectId}.cloudfunctions.net/setUserRole`
}

export async function POST(request: NextRequest): Promise<NextResponse<SetRoleResponse>> {
  try {
    // Parse request body
    const body: SetRoleRequest = await request.json()
    
    // Validate request body
    if (!body.uid || !body.role) {
      return NextResponse.json(
        { success: false, error: 'Missing uid or role' },
        { status: 400 }
      )
    }

    // Validate role
    if (body.role !== 'farm_owner' && body.role !== 'guest' && body.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Invalid role. Must be farm_owner, guest, or admin' },
        { status: 400 }
      )
    }

    // Get authorization header from the request
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: 'Missing authorization header' },
        { status: 401 }
      )
    }

    // Call the Cloud Function
    const cloudFunctionURL = getCloudFunctionURL()
    
    const response = await fetch(cloudFunctionURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
      body: JSON.stringify(body),
    })

    const result = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to set user role' },
        { status: response.status }
      )
    }

    return NextResponse.json({
      success: true,
      message: result.message || 'Role set successfully'
    })

  } catch (error) {
    console.error('Error in set-role API route:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
