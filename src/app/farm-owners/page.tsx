import { Users, TrendingUp, MessageCircle, Star, Shield, Globe } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

const benefits = [
  {
    icon: Users,
    title: 'Reach More Guests',
    description: 'Connect with hunters and photographers from around the world looking for authentic safari experiences.',
    details: [
      'Access to international market',
      'Targeted audience of safari enthusiasts',
      'Year-round booking opportunities',
      'Diverse guest demographics'
    ]
  },
  {
    icon: TrendingUp,
    title: 'Increase Revenue',
    description: 'Maximize your farm\'s earning potential with better visibility and streamlined booking management.',
    details: [
      'Higher occupancy rates',
      'Premium pricing opportunities',
      'Reduced marketing costs',
      'Direct guest relationships'
    ]
  },
  {
    icon: MessageCircle,
    title: 'Direct Communication',
    description: 'Build relationships with guests through our integrated messaging system and personalized service.',
    details: [
      'Pre-arrival guest communication',
      'Custom service offerings',
      'Guest preference tracking',
      'Repeat visitor management'
    ]
  },
  {
    icon: Star,
    title: 'Build Your Reputation',
    description: 'Showcase your farm\'s unique features and build credibility through guest reviews and ratings.',
    details: [
      'Professional farm profiles',
      'Guest review system',
      'Photo galleries',
      'Achievement badges'
    ]
  }
]

const features = [
  {
    title: 'Professional Farm Profiles',
    description: 'Create stunning profiles that showcase your farm\'s unique features, accommodations, and activities.'
  },
  {
    title: 'Booking Management',
    description: 'Manage all your bookings in one place with our intuitive dashboard and calendar system.'
  },
  {
    title: 'Guest Communication',
    description: 'Communicate directly with guests before, during, and after their stay through our messaging platform.'
  },
  {
    title: 'Analytics & Insights',
    description: 'Track your performance with detailed analytics on bookings, revenue, and guest satisfaction.'
  },
  {
    title: 'Marketing Support',
    description: 'Benefit from our marketing efforts to promote South African safari experiences globally.'
  },
  {
    title: 'Secure Payments',
    description: 'Handle payments securely with multiple payment options and transparent fee structures.'
  }
]

const requirements = [
  'Valid business registration and tourism licenses',
  'Comprehensive insurance coverage',
  'Professional hunting licenses (for hunting operations)',
  'Safety equipment and protocols in place',
  'Experienced guides and staff',
  'Quality accommodations and facilities'
]

const steps = [
  {
    step: 1,
    title: 'Create Your Account',
    description: 'Sign up as a farm owner and verify your business credentials.'
  },
  {
    step: 2,
    title: 'Build Your Profile',
    description: 'Add photos, descriptions, amenities, and pricing information.'
  },
  {
    step: 3,
    title: 'Get Verified',
    description: 'Complete our verification process to ensure quality and safety standards.'
  },
  {
    step: 4,
    title: 'Start Receiving Bookings',
    description: 'Go live and start connecting with guests from around the world.'
  }
]

export default function FarmOwnersPage() {
  return (
    <div className="min-h-screen bg-earth-100">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-earth-600 to-earth-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">Partner with BvR Safaris</h1>
            <p className="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto mb-8">
              Showcase your game farm to a global audience and grow your safari business with South Africa&apos;s premier booking platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register">
                <Button variant="secondary" size="lg">
                  Join as Farm Owner
                </Button>
              </Link>
              <Link href="/contact">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-earth-600">
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-earth-900">Why Choose BvR Safaris?</h2>
            <p className="text-lg text-earth-700 max-w-2xl mx-auto">
              Join hundreds of successful farm owners who have grown their business with our platform.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="h-full">
                <CardHeader>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mr-4">
                      <benefit.icon className="w-6 h-6 text-accent-600" />
                    </div>
                    <CardTitle className="text-xl">{benefit.title}</CardTitle>
                  </div>
                  <p className="text-earth-700">{benefit.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {benefit.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-accent-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-earth-700">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-earth-900">Platform Features</h2>
            <p className="text-lg text-earth-700">
              Everything you need to manage and grow your safari business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center p-6">
                <CardContent>
                  <h3 className="text-lg font-semibold text-earth-900 mb-3">{feature.title}</h3>
                  <p className="text-earth-700">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 bg-earth-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-earth-900">Getting Started</h2>
            <p className="text-lg text-earth-700">
              Join our platform in four simple steps.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-accent-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-lg font-semibold text-earth-900 mb-2">{step.title}</h3>
                <p className="text-earth-700">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Requirements */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Shield className="w-16 h-16 mx-auto mb-6 text-accent-600" />
            <h2 className="text-3xl font-bold text-earth-900 mb-4">Quality Standards</h2>
            <p className="text-lg text-earth-700">
              We maintain high standards to ensure the best experience for all guests.
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-center">Minimum Requirements</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {requirements.map((requirement, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-accent-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-earth-700">{requirement}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-accent-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Globe className="w-16 h-16 mx-auto mb-6" />
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Grow Your Business?</h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Join BvR Safaris today and start connecting with safari enthusiasts from around the world.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button variant="secondary" size="lg">
                Get Started Today
              </Button>
            </Link>
            <Link href="/contact">
              <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-accent-600">
                Contact Our Team
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Support Section */}
      <section className="py-12 bg-earth-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-xl font-semibold mb-4">Need Help Getting Started?</h3>
          <p className="opacity-90 mb-6">
            Our team is here to help you set up your farm profile and start receiving bookings.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" className="text-white hover:underline">
              <EMAIL>
            </a>
            <span className="hidden sm:inline">|</span>
            <a href="tel:+27011234567" className="text-white hover:underline">
              +27 (0)11 123 4567
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
