# --- 1. C<PERSON> Monorepo Root and Basic Directory Structure ---

echo "Initializing Git repository..."
git init

echo "Creating top-level directories: apps, packages, supabase"
mkdir apps
mkdir packages
mkdir supabase
echo "Monorepo base structure created."
echo "-----------------------------------"

# --- 2. Set up Supabase Project (using Deno where possible for CLI) ---
echo "Setting up Supabase project..."

# Install Supabase CLI (Option: Manual binary download if avoiding npm entirely for CLI)
# Go to https://github.com/supabase/cli/releases, download the appropriate binary for your OS,
# place it in your PATH, or call it directly.
# For this script, I'll assume `supabase` command is available in PATH.
# If you *must* use a package manager for the CLI and prefer Deno:
# As of my last update, there isn't a direct `deno install supabase-cli` type command
# that's officially maintained like the npm/binary options.
# So, npm/binary/Homebrew/Scoop are the common ways to get the CLI itself.
# For now, I'll proceed assuming you've made Supabase CLI available.

# Login to Supabase CLI
echo "Logging into Supabase CLI (ensure 'supabase' command is in your PATH)..."
supabase login

cd supabase

echo "Linking to your Supabase project. Replace YOUR_PROJECT_REF."
read -p "Enter your Supabase Project Ref ID: " project_ref
supabase link --project-ref ${project_ref}

echo "Creating an example Supabase Edge Function (Deno-based)..."
supabase functions new hello-deno
# This creates `supabase/functions/hello-deno/index.ts`
# You will manage Deno dependencies for functions via URL imports or import_map.json.

cd .. # Back to monorepo root
echo "Supabase project linked. Edge Functions will use Deno."
echo "-----------------------------------"

# --- 3. Set up Next.js Frontend App (`apps/web`) ---
# This part conventionally uses Node.js tooling (npx/npm/yarn/pnpm) for the best experience.
echo "Setting up Next.js frontend application in apps/web..."
echo "NOTE: 'create-next-app' uses Node.js tooling (npx/npm)."
cd apps

npx create-next-app@latest web --typescript --tailwind --eslint --app --src-dir
# This will create a package.json and node_modules for the 'web' app.

echo "Next.js app 'web' created (uses Node.js package management)."
cd .. # Back to monorepo root
echo "-----------------------------------"

echo "Project setup initiated!"
echo ""
echo "--- Key Points for Deno Usage ---"
echo "* Supabase Edge Functions in \`supabase/functions/\` are pure Deno TypeScript."
echo "* The Next.js app in \`apps/web/\` is initialized and typically managed using Node.js package managers (npm/yarn/pnpm) for its specific dependencies (React, Next, etc.)."
echo "* You can use Deno as a task runner (via \`deno.json\`) to execute commands for any part of the project."
echo ""
echo "--- Next Steps ---"
echo "1. Define DB schema in \`supabase/migrations/\`."
echo "2. Local Supabase: \`supabase start\` (Docker needed)."
echo "3. Next.js dev: \`cd apps/web && npm run dev\`."
echo "4. Edge Functions: Develop in \`supabase/functions/\`. Deploy with \`supabase functions deploy <function_name>\`."